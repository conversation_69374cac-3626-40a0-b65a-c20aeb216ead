import {
	req
} from '../../utils/req.js';
export default {
	// 用户信息
	// userInfo(param) {
	// 	return req.get("/massage/app/IndexUser/userInfo", param)
	// },
	userInfo(param) {
		return req.get("user/info", param)
	},
	// loginuserInfo(param) {
	// 	return req.post("v1/wxLogin", param)
	// },
	loginuserInfo(param) {
		return req.post("user/login/wxLogin", param)
	},
	updataInfo(param) {
		return req.post("user/update", param)
	},

	// 更新用户信息
	userUpdate(param) {
		return req.post("/massage/app/IndexUser/userUpdate", param)
	},
	// 获取手机号
	reportPhone(param) {
		return req.post("/massage/app/IndexUser/reportPhone", param)
	},
	// 验证码
	sendShortMsg(param) {
		return req.post("massage/app/IndexUser/sendShortMsg", param)
	},
	// 绑定手机号
	bindUserPhone(param) {
		return req.post("massage/app/IndexUser/bindUserPhone", param)
	},
	// 用户注册/绑定手机号
	register(param) {
		return req.post("user/register", param)
	},
	// 检查APP版本更新
	checkAppVersion(param) {
		return req.post("app/checkVersion", param)
	},
}
