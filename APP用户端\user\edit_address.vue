
<template>
	<view class="page">
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
			keyName="title" @cancel="showCity = false" @confirm="confirmCity" v-if="flag"></u-picker>
		<u-modal :show="show" :title="title" :content="content" @confirm="DelAddress" @cancel="show = false"
			showCancelButton></u-modal>
		<view class="top">个人信息隐私信息完全保密</view>
		<view class="main">
			<view class="main_item " @tap="goMap">
				<view class="name">服务地址</view>
				<view class="address">
					<span>{{form.address}}</span>
				</view>
				<image src="../static/images/position.png" mode=""></image>
			</view>
			<view class="main_item">
				<view class="name">门牌号</view>
				<input type="text" v-model="form.houseNumber" placeholder="请输入详细地址，如7栋4单元18a">
			</view>
			<view class="main_item">
				<view class="name">联系人</view>
				<input type="text" v-model="form.userName" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="name">性别</view>
				<view class="box">
					<view class="box_item"
						:style="form.sex == 1?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''"
						@click="selectGender(1)">先生</view>
					<view class="box_item"
						:style="form.sex == 2?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''"
						@click="selectGender(2)">女士</view>
				</view>
			</view>
			<view class="main_item">
				<view class="name">手机号码</view>
				<input type="tel" v-model="form.mobile" placeholder="请输入手机号码">
			</view>
			<view class="main_item last">
				<view class="name">设为默认地址</view>
				<u-switch v-model="form.status" activeColor="#2E80FE"></u-switch>
			</view>
		</view>
		<view class="btn" @click="SaveAddress">保存</view>
		<view class="btnD" @click="show = true">删除</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				flag: false,
				loading: false,
				showCity: false,
				content: '确认删除这条地址吗',
				title: '删除',
				show: false,
				id: '',
				form: {
					userName: '',
					mobile: '',
					address: '点击选择服务地址',
					addressInfo: '',
					houseNumber: '',
					city: '',
					cityIds: '',
					lng: '',
					lat: '',
					sex: 1,
					status: false,
					provinceId: 0,
					cityId: 0,
					areaId: 0
				},
				columnsCity: [
					[], // Province
					[], // City
					[]  // Area
				],
			}
		},
		onLoad(options) {
			this.id = options.id || '';
			this.getAddressDetail();
		},
  methods: {
    // 解析城市信息的通用方法
    parseCityInfo(address) {
      if (!address || typeof address !== 'string') {
        return { cityIds: '', city: '' };
      }

      // 处理各种地址格式的正则表达式
      const patterns = [
        // 标准格式：省+市+区/县
        /^(.+?省)(.+?市)(.+?[县区]).*$/,
        // 自治区格式：自治区+市+区/县/旗
        /^(.+?自治区)(.+?市)(.+?[县区旗]).*$/,
        // 自治区+盟+市格式：自治区+盟+市
        /^(.+?自治区)(.+?盟)(.+?市).*$/,
        // 直辖市格式：市+区/县
        /^(北京|上海|天津|重庆)(市)?(.+?[县区]).*$/,
        // 特别行政区格式
        /^(香港|澳门)(.+?区)?(.*)$/
      ];

      for (let pattern of patterns) {
        const match = address.match(pattern);
        if (match) {
          let province, city, area;

          if (pattern.source.includes('北京|上海|天津|重庆')) {
            // 直辖市处理
            province = match[1];
            city = match[1] + '市';
            area = match[3] || '';
          } else if (pattern.source.includes('香港|澳门')) {
            // 特别行政区处理
            province = match[1];
            city = match[1];
            area = match[2] || match[3] || '';
          } else if (pattern.source.includes('盟')) {
            // 自治区+盟+市格式处理
            province = match[1];
            city = match[2];  // 盟作为市级
            area = match[3];  // 市作为区级
          } else {
            // 标准省市区处理
            province = match[1];
            city = match[2];
            area = match[3];
          }

          // 清理空白字符
          province = province.trim();
          city = city.trim();
          area = area.trim();

          return {
            cityIds: `${province},${city},${area}`,
            city: `${province}-${city}-${area}`
          };
        }
      }

      // 如果都不匹配，返回空值
      console.warn('无法解析地址格式:', address);
      return { cityIds: '', city: '' };
    },

    goMap() {
      // Handle location selection based on platform
      // #ifdef MP-WEIXIN
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          uni.chooseLocation({
            success: (res) => {
              console.log(res);
              // 使用新的城市信息解析方法
              const cityInfo = this.parseCityInfo(res.address);
              this.form.cityIds = cityInfo.cityIds;
              console.log(this.form.newaddress);
              this.form.address = res.name || '点击选择服务地址';
              this.form.addressInfo = res.address || '';
              this.form.lng = res.longitude || '';
              this.form.lat = res.latitude || '';
            },
            fail: (err) => {
              console.error('Location selection failed:', err);
              uni.showToast({
                icon: 'none',
                title: '选择位置失败',
                duration: 1500,
              });
            },
          });
        },
        fail: (err) => {
          console.error('Location authorization failed:', err);
          uni.showToast({
            icon: 'none',
            title: '请授权位置信息',
            duration: 1500,
          });
        },
      });
      // #endif
      // #ifdef APP
      uni.chooseLocation({
        success: (res) => {
          // 使用新的城市信息解析方法
          const cityInfo = this.parseCityInfo(res.address);
          this.form.cityIds = cityInfo.cityIds;
          console.log(this.form.newaddress);
          this.form.address = res.name || '点击选择服务地址';
          this.form.addressInfo = res.address || '';
          this.form.lng = res.longitude || '';
          this.form.lat = res.latitude || '';
        },
        fail: (err) => {
          console.error('Location selection failed:', err);
          uni.showToast({
            icon: 'none',
            title: '选择位置失败',
            duration: 1500,
          });
        },
      });
      // #endif
    },
    getcity(parentId) {
      this.loading = true;
      this.$api.service
        .getCity(parentId)
        .then((res) => {
          this.columnsCity[0] = res.map((item) => ({
            id: item.id,
            title: item.name || item.title,
          }));
          return this.$api.service.getCity(res[0]?.id || 0);
        })
        .then((res1) => {
          this.columnsCity[1] = res1.map((item) => ({
            id: item.id,
            title: item.name || item.title,
          }));
          return this.$api.service.getCity(res1[0]?.id || 0);
        })
        .then((res2) => {
          this.columnsCity[2] = res2.map((item) => ({
            id: item.id,
            title: item.name || item.title,
          }));
          this.loading = false;
        })
        .catch((err) => {
          console.error('Error fetching city data:', err);
          this.loading = false;
          uni.showToast({
            icon: 'none',
            title: '无法加载城市数据',
            duration: 1500,
          });
        });
    },
    changeHandler(e) {
      const { columnIndex, index, picker = this.$refs.uPicker } = e;
      if (columnIndex === 0) {
        this.$api.service.getCity(this.columnsCity[0][index].id).then((res) => {
          const newData = res.map((item) => ({
            id: item.id,
            title: item.name || item.title,
          }));
          picker.setColumnValues(1, newData);
          this.columnsCity[1] = newData;
          this.$api.service.getCity(res[0]?.id || 0).then((res1) => {
            const newData2 = res1.map((item) => ({
              id: item.id,
              title: item.name || item.title,
            }));
            picker.setColumnValues(2, newData2);
            this.columnsCity[2] = newData2;
          });
        });
      } else if (columnIndex === 1) {
        this.$api.service.getCity(this.columnsCity[1][index].id).then((res) => {
          const newData = res.map((item) => ({
            id: item.id,
            title: item.name || item.title,
          }));
          picker.setColumnValues(2, newData);
          this.columnsCity[2] = newData;
        });
      }
    },
    confirmCity(Array) {
      // Set city string (e.g., "安徽-阜阳-临泉")
      this.form.city = Array.value
        .map((item, index) => {
          if (!item) {
            return this.columnsCity[index][0]?.title || '';
          }
          return item.title;
        })
        .filter(Boolean)
        .join('-');

      // Set cityId array and individual IDs (provinceId, cityId, areaId)
      const selectedIds = Array.value.map((e, j) => {
        if (!e) {
          return this.columnsCity[j][0]?.id || 0;
        }
        return e.id;
      });
      this.form.cityId = selectedIds;
      this.form.provinceId = selectedIds[0] || 0;
      this.form.cityId = selectedIds[1] || 0;
      this.form.areaId = selectedIds[2] || 0;

      this.showCity = false;
    },
    async DelAddress() {
      try {
        await this.$api.service.delAddress(this.id);
        uni.showToast({
          icon: 'success',
          title: '删除成功',
          duration: 1000,
        });
        setTimeout(() => {
          uni.navigateBack({ delta: 1 });
        }, 1000);
      } catch (err) {
        console.error('Error deleting address:', err);
        uni.showToast({
          icon: 'none',
          title: '删除失败',
          duration: 1500,
        });
      } finally {
        this.show = false;
      }
    },
    async SaveAddress() {
      // Validate form fields
      if (this.form.address === '点击选择服务地址') {
        uni.showToast({
          icon: 'none',
          title: '请选择服务地址',
          duration: 1500,
        });
        return;
      }
      const phoneReg = /^1[3456789]\d{9}$/;
      if (!phoneReg.test(this.form.mobile)) {
        uni.showToast({
          icon: 'none',
          title: '请填写正确的手机号',
          duration: 1500,
        });
        return;
      }
      const requiredFields = ['userName', 'mobile', 'houseNumber'];
      for (let key of requiredFields) {
        if (!this.form[key]) {
          uni.showToast({
            icon: 'none',
            title: '请填写完整信息',
            duration: 1500,
          });
          return;
        }
      }

      // Await the asynchronous call to ensure cityIds is updated
      if (this.form.cityIds) { // Only call if cityIds has a value from goMap
        try {
          const res = await this.$api.service.getZhuanhuan({
            mergeName: this.form.cityIds
          });
          console.log(res);
          if (res.data) {
            // Construct the comma-separated string from the individual IDs
              this.form.cityIds = `${res.data.provinceId},${res.data.cityId},${res.data.areaId}`;
          } else {
            this.form.cityIds = ''; // Handle cases where res.data might be null or undefined
          }
        } catch (err) {
          console.error("Error converting cityIds:", err);
          uni.showToast({
            icon: 'none',
            title: '城市信息转换失败',
            duration: 1500,
          });
          return; // Stop execution if conversion fails
        }
      }
      
      // Prepare form data for submission
      let subForm = {
        id: this.id || 0, // Include ID for updates or 0 for new
        userName: this.form.userName,
        mobile: this.form.mobile,
        houseNumber: this.form.houseNumber,
        address: this.form.address,
        addressInfo: this.form.addressInfo,
        city: this.form.city,
        // The original `cityId` was an array, but server expects a single ID.
        // If the server expects an areaId for `cityId`, then `this.form.areaId` is the correct value.
        // Assuming `cityId` should be the areaId based on your `confirmCity` logic setting `form.cityId` to `selectedIds[1] || 0` and then `form.areaId` to `selectedIds[2] || 0`.
        // To be explicit and safer, I'll use `this.form.areaId` for the `cityId` property in `subForm` if that's what the backend truly expects.
        // If `cityId` on the backend means the actual city ID (not area), you'll need to adjust.
        cityId: this.form.areaId, 
        provinceId: this.form.provinceId, // Include provinceId
        areaId: this.form.areaId,       // Include areaId
        cityIds: this.form.cityIds,     // Use the converted comma-separated string
        lng: this.form.lng,
        lat: this.form.lat,
        sex: this.form.sex,
        status: this.form.status ? 1 : 0,
      };

      console.log('Submitting form:', subForm);

      try {
        await this.$api.service.UpdateAddress(subForm);
        uni.showToast({
          icon: 'success',
          title: '保存成功',
          duration: 1000,
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      } catch (err) {
        console.error('Error saving address:', err);
        uni.showToast({
          icon: 'none',
          title: '保存失败',
          duration: 1500,
        });
      }
    },
  },
  onLoad(options) {
    this.id = options.id || '';
	   this.$api.service.getAddressDetail(this.id).then(res=>{
		   console.log(res)
		   if(res.code==='200'){
			   this.form={...res.data,status:res.data.status===1?true:false}
		   }else{
			   // uni.showToast({
			   //   icon: 'none',
			   //   title: '保存失败',
			   //   duration: 1500,
			   // });
		   }
	   })
	   
    // const editAdress = uni.getStorageSync('editAdress');
    // if (editAdress) {
    //   console.log(editAdress);
    //   // Populate form with editAdress data
    //   this.form = {
    //     ...this.form,
    //     houseNumber: editAdress.houseNumber || '',
    //     userName: editAdress.userName || '',
    //     mobile: editAdress.mobile || '',
    //     sex: editAdress.sex || 1,
    //     address: editAdress.address || '点击选择服务地址',
    //     addressInfo: editAdress.addressInfo || '',
    //     city: editAdress.city || '',
    //     // When loading, ensure cityId (for picker selection) is an array if `cityIds` is a string
    //     cityId: editAdress.cityId || (editAdress.cityIds ? editAdress.cityIds.split(',').map(Number) : []),
    //     lng: editAdress.lng || '',
    //     lat: editAdress.lat || '',
    //     userId: editAdress.userId || '',
    //     status: !!editAdress.status,
    //     areaId: editAdress.areaId || 0,
    //     provinceId: editAdress.provinceId || 0,
    //     // Keep cityIds as string if it comes from storage as such, will be converted before submission if needed
    //     cityIds: editAdress.cityIds || null, 
    //     createTime: editAdress.createTime || 0,
    //     top: editAdress.top || 0,
    //     uniacid: editAdress.uniacid || 666,
    //   };
    // }
    // this.getcity(0); // Load city data
  },
};
</script>

<style scoped lang="scss">
.page {
  height: 100vh;
  background-color: #fff;

  .top {
    width: 750rpx;
    height: 58rpx;
    background: #fff7f1;
    font-size: 28rpx;
    font-weight: 400;
    color: #fe921b;
    line-height: 58rpx;
    text-align: center;
  }
  .btn {
    margin: 0 auto;
    margin-top: 88rpx;
    width: 690rpx;
    height: 98rpx;
    background: #2e80fe;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    line-height: 98rpx;
    text-align: center;
  }
  .btnD {
    margin: 0 auto;
    margin-top: 40rpx;
    width: 690rpx;
    height: 98rpx;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #2e80fe;
    line-height: 98rpx;
    text-align: center;
    border: 2rpx solid #2e80fe;
  }

  .main {
    padding: 0 30rpx;

    .main_item {
      padding: 40rpx 0;
      border-bottom: 2rpx solid #e9e9e9;
      display: flex;
      align-items: center;
      position: relative;

      .name {
        width: 112rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        margin-right: 40rpx;
      }

      .address {
        font-size: 28rpx;
        font-weight: 400;
        color: #adadad;
      }

      image {
        width: 23rpx;
        height: 27rpx;
        position: absolute;
        right: 0;
        top: 46rpx;
      }
      input {
        width: 450rpx;
        font-size: 28rpx;
        color: #333333;
      }
      .box {
        display: flex;
        align-items: center;
        .box_item {
          margin-right: 20rpx;
          width: 88rpx;
          height: 50rpx;
          background: #ffffff;
          border-radius: 4rpx;
          border: 2rpx solid #ededed;
          font-size: 28rpx;
          font-weight: 400;
          color: #adadad;
          line-height: 46rpx;
          text-align: center;
        }
      }
    }
    .last {
      justify-content: space-between;
      .name {
        width: 170rpx;
      }
    }
  }
}
</style>
```