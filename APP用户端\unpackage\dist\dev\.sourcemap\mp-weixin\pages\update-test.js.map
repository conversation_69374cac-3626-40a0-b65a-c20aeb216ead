{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/update-test.vue?8f47", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/update-test.vue?05f9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/update-test.vue?80f6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/update-test.vue?c18a", "uni-app:///pages/update-test.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/update-test.vue?0553", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/update-test.vue?d121"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "currentVersion", "platform", "isChecking", "logs", "onLoad", "methods", "getCurrentVersion", "appUpdate", "testCheckUpdate", "silent", "showLoading", "testSilentCheck", "testForceUpdate", "latestVersion", "description", "wgtUrl", "forceUpdate", "testOptionalUpdate", "navigateToUpdatePage", "uni", "url", "success", "fail", "addLog", "time", "content", "console", "clearLogs"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACwD92B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAD;kBACAE;kBACAC;gBACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAJ;kBACAE;kBACAC;gBACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAE;MACA;MAEA;QACAC;QACAC;QACAC;QACAC;MACA;MAEAT;MACA;IACA;IAEA;AACA;AACA;IACAU;MACA;MAEA;QACAJ;QACAC;QACAC;QACAC;MACA;MAEAT;MACA;IACA;IAEA;AACA;AACA;IACAW;MAAA;MACA;MACAC;QACAC;QACAC;UACA;QACA;QACAC;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MAEA;QACAC;QACAC;MACA;;MAEA;MACA;QACA;MACA;MAEAC;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/update-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/update-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./update-test.vue?vue&type=template&id=41386fa6&scoped=true&\"\nvar renderjs\nimport script from \"./update-test.vue?vue&type=script&lang=js&\"\nexport * from \"./update-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./update-test.vue?vue&type=style&index=0&id=41386fa6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"41386fa6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/update-test.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./update-test.vue?vue&type=template&id=41386fa6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./update-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./update-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"test-page\">\n    <view class=\"header\">\n      <text class=\"title\">热更新功能测试</text>\n    </view>\n\n    <view class=\"test-section\">\n      <view class=\"section-title\">当前版本信息</view>\n      <view class=\"info-item\">\n        <text class=\"label\">版本号:</text>\n        <text class=\"value\">{{ currentVersion }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"label\">平台类型:</text>\n        <text class=\"value\">{{ platform === 2 ? '用户端' : '师傅端' }}</text>\n      </view>\n    </view>\n\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试功能</view>\n      \n      <button class=\"test-btn\" @click=\"testCheckUpdate\" :disabled=\"isChecking\">\n        {{ isChecking ? '检查中...' : '测试版本检查' }}\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testSilentCheck\" :disabled=\"isChecking\">\n        {{ isChecking ? '检查中...' : '测试静默检查' }}\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testForceUpdate\">\n        模拟强制更新\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testOptionalUpdate\">\n        模拟可选更新\n      </button>\n      \n      <button class=\"test-btn\" @click=\"navigateToUpdatePage\">\n        打开更新页面\n      </button>\n    </view>\n\n    <view class=\"test-section\">\n      <view class=\"section-title\">测试日志</view>\n      <scroll-view class=\"log-container\" scroll-y>\n        <view class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\">\n          <text class=\"log-time\">{{ log.time }}</text>\n          <text class=\"log-content\">{{ log.content }}</text>\n        </view>\n      </scroll-view>\n      <button class=\"clear-btn\" @click=\"clearLogs\">清空日志</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport appUpdate from '@/utils/app-update.js'\n\nexport default {\n  name: 'UpdateTest',\n  data() {\n    return {\n      currentVersion: '1.0.0',\n      platform: 2,\n      isChecking: false,\n      logs: []\n    }\n  },\n  onLoad() {\n    this.getCurrentVersion()\n    this.addLog('页面加载完成')\n  },\n  methods: {\n    /**\n     * 获取当前版本\n     */\n    async getCurrentVersion() {\n      this.currentVersion = await appUpdate.getCurrentVersion()\n      this.addLog(`获取当前版本: ${this.currentVersion}`)\n    },\n\n    /**\n     * 测试版本检查\n     */\n    async testCheckUpdate() {\n      this.isChecking = true\n      this.addLog('开始测试版本检查...')\n      \n      try {\n        await appUpdate.checkUpdate({\n          silent: false,\n          showLoading: true\n        })\n        this.addLog('版本检查完成')\n      } catch (error) {\n        this.addLog(`版本检查失败: ${error}`)\n      } finally {\n        this.isChecking = false\n      }\n    },\n\n    /**\n     * 测试静默检查\n     */\n    async testSilentCheck() {\n      this.isChecking = true\n      this.addLog('开始测试静默检查...')\n      \n      try {\n        await appUpdate.checkUpdate({\n          silent: true,\n          showLoading: false\n        })\n        this.addLog('静默检查完成')\n      } catch (error) {\n        this.addLog(`静默检查失败: ${error}`)\n      } finally {\n        this.isChecking = false\n      }\n    },\n\n    /**\n     * 模拟强制更新\n     */\n    testForceUpdate() {\n      this.addLog('模拟强制更新场景')\n      \n      const mockUpdateInfo = {\n        latestVersion: '1.2.0',\n        description: '这是一个模拟的强制更新，包含重要安全修复',\n        wgtUrl: 'https://example.com/mock_update.wgt',\n        forceUpdate: 1\n      }\n      \n      appUpdate.showUpdateDialog(mockUpdateInfo)\n      this.addLog('显示强制更新对话框')\n    },\n\n    /**\n     * 模拟可选更新\n     */\n    testOptionalUpdate() {\n      this.addLog('模拟可选更新场景')\n      \n      const mockUpdateInfo = {\n        latestVersion: '1.1.5',\n        description: '这是一个模拟的可选更新，优化了用户体验',\n        wgtUrl: 'https://example.com/mock_update.wgt',\n        forceUpdate: 0\n      }\n      \n      appUpdate.showUpdateDialog(mockUpdateInfo)\n      this.addLog('显示可选更新对话框')\n    },\n\n    /**\n     * 打开更新页面\n     */\n    navigateToUpdatePage() {\n      this.addLog('导航到更新页面')\n      uni.navigateTo({\n        url: '/pages/app-update',\n        success: () => {\n          this.addLog('成功打开更新页面')\n        },\n        fail: (error) => {\n          this.addLog(`打开更新页面失败: ${JSON.stringify(error)}`)\n        }\n      })\n    },\n\n    /**\n     * 添加日志\n     */\n    addLog(content) {\n      const now = new Date()\n      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`\n      \n      this.logs.unshift({\n        time,\n        content\n      })\n      \n      // 限制日志数量\n      if (this.logs.length > 50) {\n        this.logs = this.logs.slice(0, 50)\n      }\n      \n      console.log(`[${time}] ${content}`)\n    },\n\n    /**\n     * 清空日志\n     */\n    clearLogs() {\n      this.logs = []\n      this.addLog('日志已清空')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.test-page {\n  padding: 20rpx;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  padding: 40rpx 0;\n  \n  .title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.test-section {\n  background: #fff;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .section-title {\n    font-size: 32rpx;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 20rpx;\n    border-bottom: 2rpx solid #eee;\n    padding-bottom: 10rpx;\n  }\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 15rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  .label {\n    color: #666;\n    font-size: 28rpx;\n  }\n  \n  .value {\n    color: #333;\n    font-size: 28rpx;\n    font-weight: 500;\n  }\n}\n\n.test-btn {\n  width: 100%;\n  height: 80rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  border: none;\n  border-radius: 40rpx;\n  font-size: 30rpx;\n  margin-bottom: 20rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n  \n  &:disabled {\n    background: #ccc;\n  }\n  \n  &:active:not(:disabled) {\n    opacity: 0.8;\n  }\n}\n\n.log-container {\n  height: 400rpx;\n  border: 1rpx solid #eee;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  background: #fafafa;\n  \n  .log-item {\n    display: flex;\n    margin-bottom: 10rpx;\n    font-size: 24rpx;\n    \n    .log-time {\n      color: #999;\n      margin-right: 20rpx;\n      min-width: 120rpx;\n    }\n    \n    .log-content {\n      color: #333;\n      flex: 1;\n      word-break: break-all;\n    }\n  }\n}\n\n.clear-btn {\n  width: 100%;\n  height: 60rpx;\n  background: #f56565;\n  color: #fff;\n  border: none;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  margin-top: 20rpx;\n  \n  &:active {\n    opacity: 0.8;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./update-test.vue?vue&type=style&index=0&id=41386fa6&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./update-test.vue?vue&type=style&index=0&id=41386fa6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754707296742\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}