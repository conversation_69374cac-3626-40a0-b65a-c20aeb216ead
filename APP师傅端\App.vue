<script>
	import $api from "@/api/index.js"
	import $store from "@/store/index.js"
	import appUpdate from '@/utils/app-update.js'

	// #ifdef APP-PLUS
	var jpushModule = uni.requireNativePlugin("JG-JPush");
	var audioObj = uni.getBackgroundAudioManager();
	// #endif

	export default {
		// globalData: {
		//     primaryColor: '#A40035', // Default fallback, matching your sidebar’s color
		//   },
		data() {
			return {
				registrationID: '',
				connectStatus: ''
			}
		},

		async mounted() {
			// #ifdef H5
			if (typeof window.entryUrl === 'undefined' || window.entryUrl === '') {
				window.entryUrl = window.location.href.split('#')[0]
			}
			if (window.location.href.indexOf('?#') < 0) {
				window.location.href = window.location.href.replace("#", "?#");
			}
			// #endif
			console.log('App mounted')
		},
	
		async onLaunch() {
			// #ifdef APP-PLUS
			// 检查极光推送模块是否可用
			if (jpushModule) {
				try {
					// 初始化极光推送
					jpushModule.initJPushService();
					jpushModule.setLoggerEnable(true);
					plus.screen.lockOrientation("portrait-primary");

					// 监听极光推送连接状态
					this.getNotificationEnabled();
					jpushModule.addConnectEventListener(result => {
						let connectEnable = result.connectEnable
						uni.$emit('connectStatusChange', connectEnable)
					});

					// 监听推送消息
					jpushModule.addNotificationListener(result => {
						jpushModule.setBadge(0);
						plus.runtime.setBadgeNumber(0);
						let notificationEventType = result.notificationEventType
						console.log("通知", result, notificationEventType)

						// 点击事件处理
						if (notificationEventType == 'notificationOpened') {
							// 这里可以根据推送内容进行页面跳转
							let extras = result.extras || {};
							if (extras.page) {
								uni.navigateTo({
									url: extras.page
								});
							}
						}
					});

					// 监听连接状态变化
					uni.$on('connectStatusChange', (connectStatus) => {
						var connectStr = ''
						if (connectStatus == true) {
							connectStr = '已连接'
							this.getRegistrationID()
						} else {
							connectStr = '未连接'
						}
						console.log('监听到了连接状态变化 --- ', connectStr)
						this.connectStatus = connectStr
					})

					// 检查推送状态
					jpushModule.isPushStopped(res => {
						const { code } = res
						console.log(res, '推送连接状态');
					})

					console.log('极光推送初始化成功');
				} catch (error) {
					console.error('极光推送初始化失败:', error);
				}
			} else {
				console.warn('极光推送模块未找到，请检查插件配置或使用自定义调试基座');
			}
			// #endif

			let configInfo = uni.getStorageSync('configInfo') || ''
			if (configInfo) {
				$store.commit('updateConfigItem', {
					key: 'configInfo',
					val: configInfo
				})
			}

			let arr = ['autograph', 'userInfo', 'location', 'appLogin']
			arr.map(key => {
				let val = uni.getStorageSync(key) || ''
				if (val) {
					$store.commit('updateUserItem', {
						key,
						val
					})
				}
			})
			let commonOptions = $store.state.user.commonOptions
			let {
				channel_id = 0
			} = commonOptions
			if (channel_id) {
				commonOptions.channel_id = 0
				$store.commit('updateUserItem', {
					key: 'commonOptions',
					val: commonOptions
				})
			}
			let {
				primaryColor = ''
			} = $store.state.config.configInfo
			if (primaryColor) return
			await this.getBaseConfig()

			// #ifdef APP-PLUS
			console.log('=== APP启动完成，准备延迟检查更新 ===')
			setTimeout(() => {
				console.log('=== 3秒延迟结束，开始执行更新检查 ===')
				this.checkAppUpdateOnLaunch()
			}, 3000) // 延迟3秒检查，避免影响启动速度
			// #endif

			// #ifndef APP-PLUS
			console.log('=== 非APP环境，跳过更新检查 ===')
			// #endif
		},
		async onShow() {
			console.log('App Show')
	
		},
		onHide() {
			console.log('App Hide')
		},
		methods: {
			// 获取registrationID
			getRegistrationID() {
				// #ifdef APP-PLUS
				if (jpushModule) {
					jpushModule.getRegistrationID(result => {
						let registerID = result.registerID
						console.log('registrationID:', registerID)
						this.registrationID = registerID
						uni.setStorageSync("registerID", registerID)
					})
				} else {
					console.warn('极光推送模块未找到，无法获取registrationID');
				}
				// #endif
			},

			// 检查通知权限
			getNotificationEnabled() {
				// #ifdef APP-PLUS
				if (jpushModule) {
					if (uni.getSystemInfoSync().platform == "ios") {
						jpushModule.requestNotificationAuthorization((result) => {
							let status = result.status
							if (status < 2) {
								this.noticMsgTool()
							}
						})
					} else {
						jpushModule.isNotificationEnabled((result) => {
							if (result.code == 0) {
								this.noticMsgTool()
							}
						})
					}
				} else {
					console.warn('极光推送模块未找到，无法检查通知权限');
				}
				// #endif
			},

			// 引导用户开启通知权限
			noticMsgTool() {
				// #ifdef APP-PLUS
				if (uni.getSystemInfoSync().platform == "ios") {
					uni.showModal({
						title: '通知权限开启提醒',
						content: '您还没有开启通知权限，无法接受到消息通知，请前往设置！',
						showCancel: false,
						confirmText: '去设置',
						success: function(res) {
							if (res.confirm) {
								var app = plus.ios.invoke('UIApplication', 'sharedApplication');
								var setting = plus.ios.invoke('NSURL', 'URLWithString:', 'app-settings:');
								plus.ios.invoke(app, 'openURL:', setting);
								plus.ios.deleteObject(setting);
								plus.ios.deleteObject(app);
							}
						}
					});
				} else {
					var main = plus.android.runtimeMainActivity();
					var pkName = main.getPackageName();
					var uid = main.getApplicationInfo().plusGetAttribute("uid");
					uni.showModal({
						title: '通知权限开启提醒',
						content: '您还没有开启通知权限，无法接受到消息通知，请前往设置！',
						showCancel: false,
						confirmText: '去设置',
						success: function(res) {
							if (res.confirm) {
								var Intent = plus.android.importClass('android.content.Intent');
								var Build = plus.android.importClass("android.os.Build");
								if (Build.VERSION.SDK_INT >= 26) {
									var intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
									intent.putExtra('android.provider.extra.APP_PACKAGE', pkName);
								} else if (Build.VERSION.SDK_INT >= 21) {
									var intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
									intent.putExtra("app_package", pkName);
									intent.putExtra("app_uid", uid);
								} else {
									var Settings = plus.android.importClass("android.provider.Settings");
									var Uri = plus.android.importClass("android.net.Uri");
									intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
									var uri = Uri.fromParts("package", pkName, null);
									intent.setData(uri);
								}
								main.startActivity(intent);
							}
						}
					});
				}
				// #endif
			},

			async getBaseConfig() {
				let config = await $api.base.getConfig()
				if (!config.primaryColor) {
					config.primaryColor = '#A40035'
				}
				if (!config.subColor) {
					config.subColor = '#F1C06B'
				}
				let configInfo = Object.assign($store.state.config.configInfo, config)
				$store.commit('updateConfigItem', {
					key: 'configInfo',
					val: configInfo
				})
			},

			/**
			 * 启动时检查APP更新
			 * 如果有更新则弹窗提醒，没有更新则静默处理
			 */
			async checkAppUpdateOnLaunch() {
				// #ifdef APP-PLUS
				try {
					console.log('=== 开始检查APP更新 ===')

					// 获取当前版本
					const currentVersion = await appUpdate.getCurrentVersion()
					console.log('当前版本:', currentVersion)

					// 调用后端接口检查更新
					const response = await $api.user.checkAppVersion({
						version: currentVersion,
						platform: 1 // 师傅端
					})

					console.log('版本检查响应:', JSON.stringify(response))

					if (response.code === '200' && response.data) {
						const updateInfo = response.data
						console.log('更新信息:', JSON.stringify(updateInfo))

						if (updateInfo.needUpdate) {
							console.log('=== 发现新版本，显示更新提醒 ===')
							// 有更新时显示弹窗
							appUpdate.showUpdateDialog(updateInfo)
						} else {
							console.log('=== 已是最新版本，无需更新 ===')
						}
					} else {
						console.log('检查更新失败:', response.msg || '未知错误')
						// 如果是网络错误或服务器错误，可以尝试使用默认的更新检查
						console.log('尝试使用默认更新检查方法...')
						await appUpdate.checkUpdate({ silent: true, showLoading: false })
					}
				} catch (error) {
					console.error('检查更新异常:', error)
					// 发生异常时也尝试使用默认的更新检查
					try {
						await appUpdate.checkUpdate({ silent: true, showLoading: false })
					} catch (fallbackError) {
						console.error('默认更新检查也失败:', fallbackError)
					}
				}
				// #endif
			}
		}
	}
</script>

<style lang="scss">
	@import "uview-ui/index.scss";
	@import "/styles/index.wxss";

	/* #ifdef H5 */
	uni-page-head {
		display: none;
	}

	/* #endif */
	page {
		font-size: 28rpx;
		color: #222;
		line-height: 1.5;
		background: #fff;
		font-family: 'MyFont';
	}

	input {
		// font-family: PingFangSC-Medium, PingFang SC, -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
	}

	input::-webkit-input-placeholder {
		/* WebKit browsers */
		color: #A9A9A9;
	}

	input:-moz-placeholder {
		/* Mozilla Firefox 4 to 18 */
		color: #A9A9A9;
	}

	input::-moz-placeholder {
		/* Mozilla Firefox 19+ */
		color: #A9A9A9;
	}

	input:-ms-input-placeholder {
		/* Internet Explorer 10+ */
		color: #A9A9A9;
	}

	view {
		box-sizing: border-box;
	}

	image {
		display: block;
	}

	/*隐藏滚动条*/
	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}

	/* #ifdef MP-BAIDU */
	.swan-button.swan-button-radius-ios {
		border-radius: 0;
	}

	/* #endif */
</style>