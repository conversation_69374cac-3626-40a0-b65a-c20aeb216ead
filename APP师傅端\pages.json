{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [

		{
			"path" : "pages/shifuIndex",
			"style" : 
			{
				"navigationBarTitleText" : "接单大厅"
			}
		}
,
		{
			"path" : "pages/mineIndex",
			"style" : 
			{
				"navigationBarTitleText" : "我的"
			}
		},
		{
			"path": "pages/service",
			"style": {
				"navigationBarTitleText": "首页",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/technician",
			"style": {
				"navigationBarTitleText": "分类",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/mine",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": true,
				"navigationBarBackgroundColor": "#599eff"
			}
		},

		{
			"path": "pages/login",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},

		{
			"path": "pages/order",
			"style": {
				"navigationBarTitleText": "购物车",
				"enablePullDownRefresh": true
			}
		},




		{
			"path": "pages/welfare",
			"style": {
				"navigationBarTitleText": "福利中心"
			}
		}, {
			"path": "pages/Notice",
			"style": {
				"navigationBarTitleText": "公告中心",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/after_sale",
			"style": {
				"navigationBarTitleText": "售后服务",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/Margin",
			"style": {
				"navigationBarTitleText": "保证金",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/tuicause",
			"style": {
				"navigationBarTitleText": "退款",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/coachCashOut",
			"style": {
				"navigationBarTitleText": "提现",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/app-update",
			"style": {
				"navigationBarTitleText": "版本更新",
				"enablePullDownRefresh": false
			}
		}
	],
	"subPackages": [
		// 用户端	
		{
			"root": "user",
			"pages": [



				{
					"path": "add_address",
					"style": {
						"navigationBarTitleText": "新增地址"
					}
				},
				{
					"path": "choose_master",
					"style": {
						"navigationBarTitleText": "选择师傅"
					}
				},
				{
					"path": "addcard",
					"style": {
						"navigationBarTitleText": "添加银行卡"
					}
				},
				{
					"path": "address",
					"style": {
						"navigationBarTitleText": "我的地址",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "agent_apply",
					"style": {
						"navigationBarTitleText": "代理商申请"
					}
				},
				{
					"path": "agent",
					"style": {
						"navigationBarTitleText": "服务点列表"
					}
				},
				{
					"path": "apply_over",
					"style": {
						"navigationBarTitleText": "申请成功"
					}
				},
				{
					"path": "edit_address",
					"style": {
						"navigationBarTitleText": "编辑地址"
					}
				},
				{
					"path": "order",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "order_list",
					"style": {
						"navigationBarTitleText": "订单列表",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "order_details",
					"style": {
						"navigationBarTitleText": "订单详情"
					}
				},
				{
					"path": "order_success",
					"style": {
						"navigationBarTitleText": "下单成功"
					}
				},
				{
					"path": "userProfile",
					"style": {
						"navigationBarTitleText": "个人信息"
					}
				},

				{
					"path": "coupon",
					"style": {
						"navigationBarTitleText": "优惠券",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "repair_record",
					"style": {
						"navigationBarTitleText": "报修记录"
					}
				},
				{
					"path": "Settle",
					"style": {
						"navigationBarTitleText": "师傅入驻"
					}
				},
				{
					"path": "promotion",
					"style": {
						"navigationBarTitleText": "邀请有礼",
						"navigationBarBackgroundColor": "#F12E2C"
					}
				},

				{
					"path": "search",
					"style": {
						"navigationBarTitleText": "搜索页面"
					}
				},
				{
					"path": "commodity_details",
					"style": {
						"navigationBarTitleText": "商品详情"
					}
				},
				{
					"path": "price_parity",
					"style": {
						"navigationBarTitleText": "项目选配"
					}
				},
				{
					"path": "confirm_order",
					"style": {
						"navigationBarTitleText": "确认订单"
					}
				},
				{
					"path": "Cashier",
					"style": {
						"navigationBarTitleText": "收银台"
					}
				},
				{
					"path": "master_Info",
					"style": {
						"navigationBarTitleText": "修改个人信息"
					}
				},
				{
					"path": "choose_city",
					"style": {
						"navigationBarTitleText": "选择城市"
					}
				},

				{
					"path": "wait_price",
					"style": {
						"navigationBarTitleText": "等待报价"
					}
				},
				{
					"path": "bankCard",
					"style": {
						"navigationBarTitleText": "银行卡管理"
					}
				},
				{
					"path": "business",
					"style": {
						"navigationBarTitleText": "邀请有礼"
					}
				},
				{
					"path": "statistics",
					"style": {
						"navigationBarTitleText": "邀请人统计",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "Promotioncommission",
					"style": {
						"navigationBarTitleText": "邀请收益明细",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "promotion_order",
					"style": {
						"navigationBarTitleText": "邀请人订单",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cashOut",
					"style": {
						"navigationBarTitleText": "提现"
					}
				},
				{
					"path": "addevaluate",
					"style": {
						"navigationBarTitleText": "发表评价"
					}
				},
				{
					"path": "tuicause",
					"style": {
						"navigationBarTitleText": "申请退款"
					}
				},
				{
					"path": "configuser",
					"style": {
						"navigationBarTitleText": "用户隐私协议"
					}
				},
				{
					"path": "privacy",
					"style": {
						"navigationBarTitleText": "隐私协议"
					}
				},
				{
					"path" : "huodong_parity",
					"style" : 
					{
						"navigationBarTitleText" : "项目选配"
					}
				},
				{
					"path" : "huodong_order",
					"style" : 
					{
						"navigationBarTitleText" : "确认订单"
					}
				},
				{
					"path" : "huodongCashier",
					"style" : 
					{
						"navigationBarTitleText" : "订单支付"
					}
				},
				{
					"path" : "huodongaddress",
					"style" : 
					{
						"navigationBarTitleText" : "编辑地址"
					}
				},
				{
					"path" : "huodong_index",
					"style" : 
					{
						"navigationBarTitleText" : "限时活动"
					}
				},
				{
					"path" : "tiaozhuan",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				}

			]
		},
		// 技师 
		{
			"root": "shifu",
			"pages": [{
					"path": "Receiving",
					"style": {
						"navigationBarTitleText": "接单大厅",
						"navigationBarBackgroundColor": "#E41F19",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "mine",
					"style": {
						"navigationBarTitleText": "我的",
						"enablePullDownRefresh": true
						// "navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "master_my_order",
					"style": {
						"navigationBarTitleText": "我的订单",
						"navigationBarBackgroundColor": "#E41F19",
						"enablePullDownRefresh": true

					}
				},
				{
					"path": "income",
					"style": {
						"navigationBarTitleText": "服务收入",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "master_bao_list",
					"style": {
						"navigationBarTitleText": "报价列表",
						"navigationBarBackgroundColor": "#E41F19",
						 "enablePullDownRefresh": true
					}
				},
				{
					"path": "Margin",
					"style": {
						"navigationBarTitleText": "保证金",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "Settle",
					"style": {
						"navigationBarTitleText": "师傅入驻",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "master_Info",
					"style": {
						"navigationBarTitleText": "编辑师傅信息",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "promotion",
					"style": {
						"navigationBarTitleText": "邀请有礼",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "skills",
					"style": {
						"navigationBarTitleText": "技能标签",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "Professiona",
					"style": {
						"navigationBarTitleText": "技能证书",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "master_order_details",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "skillsIndex",
					"style": {
						"navigationBarTitleText": "技能标签",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "coachCashOut",
					"style": {
						"navigationBarTitleText": "提现",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "business",
					"style": {
						"navigationBarTitleText": "邀请有礼",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path": "statistics",
					"style": {
						"navigationBarTitleText": "邀请人统计",
						"navigationBarBackgroundColor": "#E41F19",
						"enablePullDownRefresh": true

					}
				},
				{
					"path": "Promotioncommission",
					"style": {
						"navigationBarTitleText": "邀请收益明细",
						"navigationBarBackgroundColor": "#E41F19",
						"enablePullDownRefresh": true

					}
				},
				{
					"path": "promotion_order",
					"style": {
						"navigationBarTitleText": "邀请人订单",
						"navigationBarBackgroundColor": "#E41F19",
						"enablePullDownRefresh": true

					}
				},
				{
					"path": "cashOut",
					"style": {
						"navigationBarTitleText": "提现",
						"navigationBarBackgroundColor": "#E41F19",
						"enablePullDownRefresh": true
					}
				},
				{
					"path" : "master_order_my",
					"style" : 
					{
						"navigationBarTitleText" : "订单详情",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path" : "userProfile",
					"style" : 
					{
						"navigationBarTitleText" : "编辑师傅信息",
						"navigationBarBackgroundColor": "#E41F19"
					}
				},
				{
					"path" : "shifuGrade",
					"style" : 
					{
						"navigationBarTitleText" : "师傅等级"
					}
				},
				{
					"path" : "coreWallet",
					"style" : 
					{
						"navigationBarTitleText" : "提现管理"
					}
				}




			]
		}
	],
	"lazyCodeLoading": "requiredComponents",
	"globalStyle": {
		"navigationBarTextStyle": "white",
		"navigationBarTitleText": "",
		"navigationBarBackgroundColor": "#599eff",
		"backgroundColor": "#fff",
		"onReachBottomDistance": 140
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}