{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?8c17", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?cafe", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?76e8", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?709d", "uni-app:///pages/login.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?da69", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/login.vue?73e6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentMode", "loginType", "showPassword", "showConfirmPassword", "agreedToTerms", "isLoading", "isWechatLoading", "smsCountdown", "smsTimer", "registerID", "loginForm", "phone", "password", "smsForm", "code", "registerForm", "shortCode", "pidInviteCode", "forgotForm", "newPassword", "confirmPassword", "computed", "canSubmit", "onLoad", "methods", "goBack", "uni", "getCurrentPlatform", "getPlatformType", "getWelcomeText", "getButtonText", "switchMode", "switchLoginType", "togglePassword", "toggleConfirmPassword", "toggleAgreement", "clearForms", "navigateToAgreement", "url", "validatePhone", "sendSmsCode", "response", "console", "startCountdown", "clearInterval", "handleSubmit", "handlePasswordLogin", "isapp", "params", "platform", "registrationId", "handleSmsLogin", "handleRegister", "token", "extractTokenFromHeaders", "autoLoginAfterRegister", "loginParams", "loginResponse", "setTimeout", "handleForgotPassword", "handleLoginSuccess", "key", "val", "userInfo", "userInfoFormatted", "avatarUrl", "nick<PERSON><PERSON>", "userId", "createTime", "pid", "inviteCode", "saveUserInfoToStorage", "handleWechatLogin", "title", "errorMessage", "checkWechatEnvironment", "reject", "service", "success", "clearTimeout", "resolve", "fail", "providers", "getWechatCode", "provider", "onlyAuthorize", "errorMsg", "getWechatUserInfo", "encryptedData", "iv", "callWechatLoginAPI", "handleWechatLoginSuccess", "userData", "unionid", "appOpenid", "getRegistrationId", "storedId", "getInviteCode", "getPasswordStrength", "getPasswordStrengthText", "showToast", "icon", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAo1B,CAAgB,o2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6Qx2B;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAF;QACAG;MACA;MACAC;QACAJ;QACAC;QACAI;QACAC;MACA;MACAC;QACAP;QACAQ;QACAC;QACAJ;MACA;IACA;EACA;EACAK;IACAC;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;MACA;QACA,+DACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;IACA;EACA;EACAC,yCACA;IAEAC;MACAC;IACA;IAEA;IACAC;MAKA;MAKA;IACA;IAEA;IACAC;MAMA;;MAOA;MACA;IACA;IAMAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QAAAzB;QAAAC;MAAA;MACA;QAAAD;QAAAG;MAAA;MACA;QAAAH;QAAAC;QAAAI;QAAAC;MAAA;MACA;QAAAN;QAAAQ;QAAAC;QAAAJ;MAAA;IACA;IAEAqB;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAZ;QAAAY;MAAA;IACA;IAIA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA7B;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,iCACA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;kBAAAA;gBAAA;cAAA;gBAAA8B;gBAEA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,IAKA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;oBACA;sBACA;oBACA;sBACA;oBACA;kBACA;gBACA;kBACA;oBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;oBACA;kBACA;oBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAH;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,mBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAGAlC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAmC;gBACAL;gBAEAM;kBACArC;kBACAC;kBACAqC;kBAAA;kBACAC;kBAAA;kBACAH;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAN;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,iBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAGArC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAiC;gBACAL;gBAEAM;kBACArC;kBACAG;kBACAmC;kBAAA;kBACAC;kBAAA;kBACAH;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAN;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,sBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAGAxC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAGAI;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACA+B;gBACAL;gBAEAM;kBACArC;kBACAC;kBACAI;kBACAC;kBACA8B;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAN;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACAY;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAX;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAR;gBACAL;gBAEAc;kBACA7C;kBACAC;kBACAqC;kBAAA;kBACAC;kBAAA;kBACAH;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAU;gBAAA;gBAAA,OAGA;cAAA;gBAEA;;gBAEA;gBACAC;kBACAhC;oBACAY;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAI;gBACA;;gBAEA;gBACAgB;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,oBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAGAxC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAGAH;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACA+B;gBACAL;gBAEAM;kBACArC;kBACAQ;kBACAC;kBACAJ;kBACA+B;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAN;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAGA;gBACAiB;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAlB;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAW;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBACAX;gBAAA,MACA;cAAA;gBAGA;gBACAhB;gBACA;kBACAmC;kBACAC;gBACA;;gBAEA;gBACAC;gBACAC;kBACArD;kBACAsD;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAEA;kBACAT;kBACAC;gBACA;;gBAEA;gBACA;gBAEA;;gBAEA;gBACAJ;kBACAhC;oBACAY;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAiC;MACA7C;MACAA;MACAA;MACAA;MACAA;MACA;QACAA;MACA;IACA;IAEA;IACA8C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA9B;gBACA;gBACAhB;kBAAA+C;gBAAA;gBAAA;gBAGA;gBACA/B;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACAA;gBACAhB;kBAAA+C;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA3D;gBACA4B;;gBAEA;gBACAA;gBACAhB;kBAAA+C;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA/B;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;;gBAEA;gBACAgC;gBACA;kBACA;kBACAhC;gBACA;kBACA;gBACA;cAAA;gBAAA;gBAEA;gBACAhB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjC;;gBAEA;gBACAO;gBACAP;;gBAEA;gBAAA,MACAO;kBAAA;kBAAA;gBAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,MAEA;cAAA;gBAIA;;gBAiCA;gBACAP;gBAAA;gBAAA;gBAAA,OAEA;kBACAA;;kBAEA;kBACA;oBACAA;oBACAkC;kBACA;;kBAEAlD;oBACAmD;oBACAC;sBACAC;sBACArC;sBACAsC;oBACA;oBACAC;sBACAF;sBACArC;sBACAkC;oBACA;kBACA;gBACA;cAAA;gBAtBAM;gBAwBAxC;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBAAA,MACA;cAAA;gBAGAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAyC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAzC;gBAAA,mCAEA;kBACAA;;kBAEA;kBACA;oBACAA;oBACAkC;kBACA;;kBAEAlC;oBACA0C;oBACAC;kBACA;kBAEA3D;oBACA;oBACA;oBAAA;oBACAoD;sBACAC;sBACArC;sBAGA;wBACAA;wBACAsC;sBACA;wBACAtC;wBACAkC;sBACA;oBACA;oBACAK;sBACAF;sBACArC;;sBAEA;sBACA;;sBAEA;sBACA;wBACA;0BACA;4BACA4C;4BACA;0BACA;4BACAA;4BACA;0BACA;4BACAA;4BACA;0BACA;4BACAA;4BACA;0BACA;4BACAA;4BACA;0BACA;4BACAA;4BACA;0BACA;4BACAA;wBAAA;sBAEA;wBACAA;sBACA;wBACAA;sBACA;sBAEAV;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAW;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA7C;;gBAEA;gBACA;gBAAA,mCACA;kBACA;kBACA;oBACAA;oBACAsC;sBACAQ;sBACAC;oBACA;kBACA;;kBAEA/D;oBACA0D;oBACAN;sBACAC;sBACArC;sBACAsC;wBACAQ;wBACAC;sBACA;oBACA;oBACAR;sBACAF;sBACArC;sBACA;sBACAsC;wBACAQ;wBACAC;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAhD;;gBAEA;gBACA;gBACA;gBAEAM;kBACAlC;kBACAmC;kBAAA;kBACAC;gBACA;gBAEAR;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAD;gBACAC;gBAAA,MAEAD;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBAAA,MACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjD;gBACAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACA;gBACA;gBACA;gBAEA;gBACAkD;gBACAlD;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA,MACA;cAAA;gBAGA;gBACAW;gBACAX;gBAEA;kBACA;kBACAhB;kBACA;oBACAmC;oBACAC;kBACA;kBACApB;gBACA;;gBAEA;gBACAsB;kBACArD;kBACAsD;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAuB;kBAAA;kBACAC;gBACA,GAEA;;gBACApD;gBACA;kBACAmB;kBACAC;gBACA;;gBAEA;gBACApB;gBACA;;gBAEA;gBACA;kBACAA;kBACAhB;gBACA;;gBAEA;gBACAgB;gBACAA;gBACAA;gBACAA;gBACAA;gBAEAA;gBACAA;;gBAEA;gBACAgB;kBACAhC;oBACAY;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAyD;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACAtD;gBAAA,mCACAsD;cAAA;gBAGA;;gBAkBAtD;gBAAA,mCACA;cAAA;gBAAA;gBAAA;gBAEAA;gBAAA,mCACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuD;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAIA;IACAC;MAAA;MACA1E;QACA+C;QACA4B;QACAC;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnxCA;AAAA;AAAA;AAAA;AAA2lD,CAAgB,+iDAAG,EAAC,C;;;;;;;;;;;ACA/mD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=18804380&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18804380\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=18804380&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getWelcomeText()\n  var m1 =\n    _vm.currentMode === \"register\"\n      ? _vm.registerForm.phone && _vm.validatePhone(_vm.registerForm.phone)\n      : null\n  var m2 =\n    _vm.currentMode === \"register\" && _vm.registerForm.password\n      ? _vm.getPasswordStrength()\n      : null\n  var m3 =\n    _vm.currentMode === \"register\" && _vm.registerForm.password\n      ? _vm.getPasswordStrength()\n      : null\n  var m4 =\n    _vm.currentMode === \"register\" && _vm.registerForm.password\n      ? _vm.getPasswordStrength()\n      : null\n  var m5 =\n    _vm.currentMode === \"register\" && _vm.registerForm.password\n      ? _vm.getPasswordStrengthText()\n      : null\n  var m6 = !_vm.isLoading ? _vm.getButtonText() : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"login-container\">\n\t\t<!-- Background Decoration -->\n\t\t<view class=\"bg-decoration\">\n\t\t\t<view class=\"circle circle-1\"></view>\n\t\t\t<view class=\"circle circle-2\"></view>\n\t\t\t<view class=\"circle circle-3\"></view>\n\t\t</view>\n\n\t\t<!-- Header -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t<u-icon name=\"arrow-left\" color=\"#3b82f6\" size=\"20\"></u-icon>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Logo Section -->\n\t\t<view class=\"logo-section\">\n\t\t\t<view class=\"logo-wrapper\">\n\t\t\t\t<image class=\"logo\" src=\"/static/images/logo-index.jpg\" mode=\"aspectFit\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"app-name\">今师傅</view>\n\t\t\t<view class=\"welcome-text\">{{ getWelcomeText() }}</view>\n\t\t</view>\n\n\t\t<!-- Main Card -->\n\t\t<view class=\"main-card\">\n\t\t\t<!-- Mode Title -->\n\t\t\t<!-- <view class=\"mode-title\">\n\t\t\t\t{{ currentMode === 'login' ? '欢迎回来' : currentMode === 'register' ? '创建账号' : '重置密码' }}\n\t\t\t</view> -->\n\n\t\t\t<!-- Login Form -->\n\t\t\t<view v-if=\"currentMode === 'login'\">\n\t\t\t\t<!-- Tab Switcher -->\n\t\t\t\t<view class=\"tab-switcher\">\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{ active: loginType === 'password' }\" @click=\"switchLoginType('password')\">\n\t\t\t\t\t\t<u-icon name=\"lock\" size=\"16\" :color=\"loginType === 'password' ? '#3b82f6' : '#94a3b8'\"></u-icon>\n\t\t\t\t\t\t<text>密码登录</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tab-item\" :class=\"{ active: loginType === 'sms' }\" @click=\"switchLoginType('sms')\">\n\t\t\t\t\t\t<u-icon name=\"chat\" size=\"16\" :color=\"loginType === 'sms' ? '#3b82f6' : '#94a3b8'\"></u-icon>\n\t\t\t\t\t\t<text>验证码登录</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- Password Login -->\n\t\t\t\t<view v-if=\"loginType === 'password'\" class=\"form-content\">\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"phone\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入手机号\" v-model=\"loginForm.phone\" maxlength=\"11\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"lock\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" :type=\"showPassword ? 'text' : 'password'\" placeholder=\"请输入密码\" v-model=\"loginForm.password\" />\n\t\t\t\t\t\t\t<view class=\"action-icon\" @click=\"togglePassword\">\n\t\t\t\t\t\t\t\t<u-icon :name=\"showPassword ? 'eye' : 'eye-off'\" color=\"#94a3b8\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"login-links\">\n\t\t\t\t\t\t<view class=\"forgot-link\" @click=\"switchMode('forgot')\">忘记密码？</view>\n\t\t\t\t\t\t<view class=\"register-link\" @click=\"switchMode('register')\">\n\t\t\t\t\t\t\t还没有账号？<text class=\"link-highlight\">立即注册</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- SMS Login -->\n\t\t\t\t<view v-if=\"loginType === 'sms'\" class=\"form-content\">\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"phone\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入手机号\" v-model=\"smsForm.phone\" maxlength=\"11\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"chat\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入验证码\" v-model=\"smsForm.code\" maxlength=\"6\" />\n\t\t\t\t\t\t\t<view class=\"sms-btn\" @click=\"sendSmsCode\" :class=\"{ disabled: smsCountdown > 0 }\">\n\t\t\t\t\t\t\t\t{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- Register Form -->\n\t\t\t<view v-if=\"currentMode === 'register'\" class=\"form-content\">\n\t\t\t\t<!-- 注册步骤指示器 -->\n\t\t\t\t\n\t\t\t\t<!-- 注册提示 -->\n\t\t\t\t\n\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<!-- 手机号输入 -->\n\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"phone\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-content\">\n\t\t\t\t\t\t\t<view class=\"input-label\">手机号</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入11位手机号\" v-model=\"registerForm.phone\" maxlength=\"11\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-status\" v-if=\"registerForm.phone && validatePhone(registerForm.phone)\">\n\t\t\t\t\t\t\t<u-icon name=\"checkmark-circle\" color=\"#52c41a\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 密码输入 -->\n\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"lock\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-content\">\n\t\t\t\t\t\t\t<view class=\"input-label\">设置密码</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" :type=\"showPassword ? 'text' : 'password'\" placeholder=\"请设置6-20位密码\" v-model=\"registerForm.password\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-icon\" @click=\"togglePassword\">\n\t\t\t\t\t\t\t<u-icon :name=\"showPassword ? 'eye' : 'eye-off'\" color=\"#94a3b8\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 密码强度指示器 -->\n\t\t\t\t\t<view class=\"password-strength\" v-if=\"registerForm.password\">\n\t\t\t\t\t\t<view class=\"strength-label\">密码强度：</view>\n\t\t\t\t\t\t<view class=\"strength-bar\">\n\t\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: getPasswordStrength() >= 1 }\"></view>\n\t\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: getPasswordStrength() >= 2 }\"></view>\n\t\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: getPasswordStrength() >= 3 }\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"strength-text\">{{ getPasswordStrengthText() }}</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 验证码输入 -->\n\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"chat\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-content\">\n\t\t\t\t\t\t\t<view class=\"input-label\">短信验证码</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入6位验证码\" v-model=\"registerForm.shortCode\" maxlength=\"6\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"sms-btn\" @click=\"sendSmsCode\" :class=\"{ disabled: smsCountdown > 0 }\">\n\t\t\t\t\t\t\t{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 邀请码输入 -->\n\t\t\t\t\t<view class=\"input-item optional\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"gift\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-content\">\n\t\t\t\t\t\t\t<view class=\"input-label\">\n\t\t\t\t\t\t\t\t邀请码\n\t\t\t\t\t\t\t\t<text class=\"optional-tag\">选填</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"text\" placeholder=\"填写邀请码可获得奖励\" v-model=\"registerForm.pidInviteCode\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-benefit\" v-if=\"registerForm.pidInviteCode\">\n\t\t\t\t\t\t\t<u-icon name=\"gift\" color=\"#ff9500\" size=\"16\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t\n\t\t\t</view>\n\n\t\t\t<!-- Forgot Password Form -->\n\t\t\t<view v-if=\"currentMode === 'forgot'\" class=\"form-content\">\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"phone\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入手机号\" v-model=\"forgotForm.phone\" maxlength=\"11\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"lock\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input class=\"input-field\" :type=\"showPassword ? 'text' : 'password'\" placeholder=\"请输入新密码\" v-model=\"forgotForm.newPassword\" />\n\t\t\t\t\t\t<view class=\"action-icon\" @click=\"togglePassword\">\n\t\t\t\t\t\t\t<u-icon :name=\"showPassword ? 'eye' : 'eye-off'\" color=\"#94a3b8\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"lock\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input class=\"input-field\" :type=\"showConfirmPassword ? 'text' : 'password'\" placeholder=\"请确认新密码\" v-model=\"forgotForm.confirmPassword\" />\n\t\t\t\t\t\t<view class=\"action-icon\" @click=\"toggleConfirmPassword\">\n\t\t\t\t\t\t\t<u-icon :name=\"showConfirmPassword ? 'eye' : 'eye-off'\" color=\"#94a3b8\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t<u-icon name=\"chat\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入验证码\" v-model=\"forgotForm.shortCode\" maxlength=\"6\" />\n\t\t\t\t\t\t<view class=\"sms-btn\" @click=\"sendSmsCode\" :class=\"{ disabled: smsCountdown > 0 }\">\n\t\t\t\t\t\t\t{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- Agreement Section -->\n\t\t\t<view class=\"agreement-section\">\n\t\t\t\t<view class=\"checkbox-container\" @click=\"toggleAgreement\">\n\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: agreedToTerms }\">\n\t\t\t\t\t\t<u-icon v-if=\"agreedToTerms\" name=\"checkmark\" color=\"#fff\" size=\"12\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"agreement-text\">\n\t\t\t\t\t\t我已阅读并同意\n\t\t\t\t\t\t<text class=\"link\" @click.stop=\"navigateToAgreement('service')\">《服务协议》</text>\n\t\t\t\t\t\t和\n\t\t\t\t\t\t<text class=\"link\" @click.stop=\"navigateToAgreement('privacy')\">《隐私政策》</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- Action Button -->\n\t\t\t<view class=\"action-button\" :class=\"{ disabled: isLoading }\" @click=\"handleSubmit\">\n\t\t\t\t<view v-if=\"isLoading\" class=\"loading-icon\">\n\n\t\t\t\t</view>\n\t\t\t\t<text>{{ isLoading ? '处理中...' : getButtonText() }}</text>\n\t\t\t</view>\n\n\t\t\t<!-- WeChat Login Button -->\n\t\t\t<view class=\"wechat-login-button\" @click=\"handleWechatLogin\" v-if=\"currentMode === 'login'\" :class=\"{ disabled: isWechatLoading }\">\n\t\t\t\t<view v-if=\"isWechatLoading\" class=\"loading-icon\">\n\n\t\t\t\t</view>\n\t\t\t\t<!-- <view class=\"wechat-icon\">\n\n\t\t\t\t</view> -->\n\t\t\t\t<text>{{ isWechatLoading ? '微信登录中...' : '微信登录' }}</text>\n\t\t\t</view>\n\n\t\t\t<!-- Switch Mode Links -->\n\t\t\t<view class=\"switch-links\">\n\t\t\t\t<view v-if=\"currentMode === 'register'\" class=\"link-text\" @click=\"switchMode('login')\">\n\t\t\t\t\t已有账号？<text class=\"link-highlight\">立即登录</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"currentMode === 'forgot'\" class=\"link-text\" @click=\"switchMode('login')\">\n\t\t\t\t\t<text class=\"link-highlight\">返回登录</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Bottom Decoration -->\n\t\t<view class=\"bottom-decoration\">\n\t\t\t<view class=\"wave\"></view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { mapMutations } from 'vuex';\n\timport { md5 } from '@/utils/md5.js';\n\timport { extractTokenFromHeaders } from '@/utils/cookieParser.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentMode: 'login', // login, register, forgot\n\t\t\t\tloginType: 'password', // password, sms\n\t\t\t\tshowPassword: false,\n\t\t\t\tshowConfirmPassword: false,\n\t\t\t\tagreedToTerms: false,\n\t\t\t\tisLoading: false,\n\t\t\t\tisWechatLoading: false, // 微信登录独立的加载状态\n\t\t\t\tsmsCountdown: 0,\n\t\t\t\tsmsTimer: null,\n\t\t\t\tregisterID:'',\n\t\t\t\t// Login forms\n\t\t\t\tloginForm: {\n\t\t\t\t\tphone: '17856179093',\n\t\t\t\t\tpassword: 'wudong123'\n\t\t\t\t},\n\t\t\t\tsmsForm: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\tcode: ''\n\t\t\t\t},\n\t\t\t\tregisterForm: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\tpassword: '',\n\t\t\t\t\tshortCode: '',\n\t\t\t\t\tpidInviteCode: ''\n\t\t\t\t},\n\t\t\t\tforgotForm: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\tnewPassword: '',\n\t\t\t\t\tconfirmPassword: '',\n\t\t\t\t\tshortCode: ''\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\tcanSubmit() {\n\t\t\t\tif (!this.agreedToTerms) return false;\n\t\t\t\t\n\t\t\t\tif (this.currentMode === 'login') {\n\t\t\t\t\tif (this.loginType === 'password') {\n\t\t\t\t\t\treturn this.loginForm.phone && this.loginForm.password;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn this.smsForm.phone && this.smsForm.code;\n\t\t\t\t\t}\n\t\t\t\t} else if (this.currentMode === 'register') {\n\t\t\t\t\treturn this.registerForm.phone && this.registerForm.password && this.registerForm.shortCode;\n\t\t\t\t} else if (this.currentMode === 'forgot') {\n\t\t\t\t\treturn this.forgotForm.phone && this.forgotForm.newPassword && \n\t\t\t\t\t\t   this.forgotForm.confirmPassword && this.forgotForm.shortCode;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t\tthis.registerID=uni.getStorageSync(\"registerID\")\n\t\t\t// 获取邀请码\n\t\t\tif (options.inviteCode) {\n\t\t\t\tthis.registerForm.pidInviteCode = options.inviteCode;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t...mapMutations(['updateUserItem']),\n\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\n\t\t\t// 检查当前平台\n\t\t\tgetCurrentPlatform() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\treturn 'app-plus';\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\treturn 'mp-weixin';\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\treturn 'h5';\n\t\t\t\t// #endif\n\t\t\t\treturn 'unknown';\n\t\t\t},\n\n\t\t\t// 获取平台类型数值\n\t\t\tgetPlatformType() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\treturn 1; // APP\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\treturn 0; // 微信小程序\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef H5\n\t\t\t\treturn 0; // H5当作小程序处理\n\t\t\t\t// #endif\n\n\t\t\t\t// 默认返回小程序\n\t\t\t\treturn 0;\n\t\t\t},\n\n\n\n\n\n\t\t\tgetWelcomeText() {\n\t\t\t\tswitch (this.currentMode) {\n\t\t\t\t\tcase 'login': return '欢迎回来，请登录您的账号';\n\t\t\t\t\tcase 'register': return '创建新账号，开始您的服务之旅';\n\t\t\t\t\tcase 'forgot': return '重置密码，找回您的账号';\n\t\t\t\t\tdefault: return '';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetButtonText() {\n\t\t\t\tswitch (this.currentMode) {\n\t\t\t\t\tcase 'login': return '登录';\n\t\t\t\t\tcase 'register': return '注册';\n\t\t\t\t\tcase 'forgot': return '重置密码';\n\t\t\t\t\tdefault: return '';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tswitchMode(mode) {\n\t\t\t\tthis.currentMode = mode;\n\t\t\t\tthis.clearForms();\n\t\t\t},\n\n\t\t\tswitchLoginType(type) {\n\t\t\t\tthis.loginType = type;\n\t\t\t},\n\n\t\t\ttogglePassword() {\n\t\t\t\tthis.showPassword = !this.showPassword;\n\t\t\t},\n\n\t\t\ttoggleConfirmPassword() {\n\t\t\t\tthis.showConfirmPassword = !this.showConfirmPassword;\n\t\t\t},\n\n\t\t\ttoggleAgreement() {\n\t\t\t\tthis.agreedToTerms = !this.agreedToTerms;\n\t\t\t},\n\n\t\t\tclearForms() {\n\t\t\t\tthis.loginForm = { phone: '', password: '' };\n\t\t\t\tthis.smsForm = { phone: '', code: '' };\n\t\t\t\tthis.registerForm = { phone: '', password: '', shortCode: '', pidInviteCode: '' };\n\t\t\t\tthis.forgotForm = { phone: '', newPassword: '', confirmPassword: '', shortCode: '' };\n\t\t\t},\n\n\t\t\tnavigateToAgreement(type) {\n\t\t\t\tlet url = '../user/configuser';\n\t\t\t\tif (type === 'service') {\n\t\t\t\t\turl += '?type=service';\n\t\t\t\t} else if (type === 'privacy') {\n\t\t\t\t\turl += '?type=privacy';\n\t\t\t\t}\n\t\t\t\tuni.navigateTo({ url });\n\t\t\t},\n\n\n\n\t\t\t// 验证手机号\n\t\t\tvalidatePhone(phone) {\n\t\t\t\tconst phoneReg = /^1[3-9]\\d{9}$/;\n\t\t\t\treturn phoneReg.test(phone);\n\t\t\t},\n\n\t\t\t// 发送短信验证码\n\t\t\tasync sendSmsCode() {\n\t\t\t\tif (this.smsCountdown > 0) return;\n\n\t\t\t\tlet phone = '';\n\t\t\t\tif (this.currentMode === 'login' && this.loginType === 'sms') {\n\t\t\t\t\tphone = this.smsForm.phone;\n\t\t\t\t} else if (this.currentMode === 'register') {\n\t\t\t\t\tphone = this.registerForm.phone;\n\t\t\t\t} else if (this.currentMode === 'forgot') {\n\t\t\t\t\tphone = this.forgotForm.phone;\n\t\t\t\t}\n\n\t\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\t\treturn this.showToast('请输入正确的手机号');\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\t// 调用发送验证码接口\n\t\t\t\t\tconst response = await this.$api.base.sendSmsCode({ phone });\n\n\t\t\t\t\tif (response.code === '200') {\n\t\t\t\t\t\tthis.showToast('验证码发送成功', 'success');\n\t\t\t\t\t\tthis.startCountdown();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.showToast(response.msg || '验证码发送失败，请重试');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('发送验证码失败:', error);\n\t\t\t\t\tthis.showToast('验证码发送失败，请重试');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 开始倒计时\n\t\t\tstartCountdown() {\n\t\t\t\tthis.smsCountdown = 60;\n\t\t\t\tthis.smsTimer = setInterval(() => {\n\t\t\t\t\tthis.smsCountdown--;\n\t\t\t\t\tif (this.smsCountdown <= 0) {\n\t\t\t\t\t\tclearInterval(this.smsTimer);\n\t\t\t\t\t\tthis.smsTimer = null;\n\t\t\t\t\t}\n\t\t\t\t}, 1000);\n\t\t\t},\n\n\t\t\t// 主要提交处理\n\t\t\tasync handleSubmit() {\n\t\t\t\tif (this.isLoading) return;\n\n\t\t\t\t// 检查是否同意协议\n\t\t\t\tif (!this.agreedToTerms) {\n\t\t\t\t\tthis.showToast('请勾选我已阅读并同意服务协议和隐私政策');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 检查表单数据\n\t\t\t\tif (!this.canSubmit) {\n\t\t\t\t\t// 根据当前模式提供具体的提示\n\t\t\t\t\tif (this.currentMode === 'login') {\n\t\t\t\t\t\tif (this.loginType === 'password') {\n\t\t\t\t\t\t\tif (!this.loginForm.phone) {\n\t\t\t\t\t\t\t\tthis.showToast('请输入手机号');\n\t\t\t\t\t\t\t} else if (!this.loginForm.password) {\n\t\t\t\t\t\t\t\tthis.showToast('请输入密码');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (!this.smsForm.phone) {\n\t\t\t\t\t\t\t\tthis.showToast('请输入手机号');\n\t\t\t\t\t\t\t} else if (!this.smsForm.code) {\n\t\t\t\t\t\t\t\tthis.showToast('请输入验证码');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (this.currentMode === 'register') {\n\t\t\t\t\t\tif (!this.registerForm.phone) {\n\t\t\t\t\t\t\tthis.showToast('请输入手机号');\n\t\t\t\t\t\t} else if (!this.registerForm.password) {\n\t\t\t\t\t\t\tthis.showToast('请设置密码');\n\t\t\t\t\t\t} else if (!this.registerForm.shortCode) {\n\t\t\t\t\t\t\tthis.showToast('请输入验证码');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (this.currentMode === 'forgot') {\n\t\t\t\t\t\tif (!this.forgotForm.phone) {\n\t\t\t\t\t\t\tthis.showToast('请输入手机号');\n\t\t\t\t\t\t} else if (!this.forgotForm.newPassword) {\n\t\t\t\t\t\t\tthis.showToast('请输入新密码');\n\t\t\t\t\t\t} else if (!this.forgotForm.confirmPassword) {\n\t\t\t\t\t\t\tthis.showToast('请确认新密码');\n\t\t\t\t\t\t} else if (!this.forgotForm.shortCode) {\n\t\t\t\t\t\t\tthis.showToast('请输入验证码');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.isLoading = true;\n\n\t\t\t\ttry {\n\t\t\t\t\tif (this.currentMode === 'login') {\n\t\t\t\t\t\tif (this.loginType === 'password') {\n\t\t\t\t\t\t\tawait this.handlePasswordLogin();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tawait this.handleSmsLogin();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (this.currentMode === 'register') {\n\t\t\t\t\t\tawait this.handleRegister();\n\t\t\t\t\t} else if (this.currentMode === 'forgot') {\n\t\t\t\t\t\tawait this.handleForgotPassword();\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('操作失败:', error);\n\t\t\t\t\tthis.showToast(error.message || '操作失败，请重试');\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 账号密码登录\n\t\t\tasync handlePasswordLogin() {\n\t\t\t\tconst { phone, password } = this.loginForm;\n\n\t\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\t\tthrow new Error('请输入正确的手机号');\n\t\t\t\t}\n\n\t\t\t\tif (!password) {\n\t\t\t\t\tthrow new Error('请输入密码');\n\t\t\t\t}\n\n\t\t\t\t// 获取平台类型\n\t\t\t\tconst isapp = this.getPlatformType();\n\t\t\t\tconsole.log('当前登录平台类型 isapp:', isapp);\n\n\t\t\t\tconst params = {\n\t\t\t\t\tphone,\n\t\t\t\t\tpassword: md5(password),\n\t\t\t\t\tplatform: 2, // 用户端\n\t\t\t\t\tregistrationId:this.registerID, // 极光推送id，暂时为空\n\t\t\t\t\tisapp: isapp // 添加平台类型参数\n\t\t\t\t};\n\n\t\t\t\t// 使用API方法\n\t\t\t\tconst response = await this.$api.base.appLoginByPass(params);\n\t\t\t\tawait this.handleLoginSuccess(response);\n\t\t\t},\n\n\t\t\t// 短信验证码登录\n\t\t\tasync handleSmsLogin() {\n\t\t\t\tconst { phone, code } = this.smsForm;\n\n\t\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\t\tthrow new Error('请输入正确的手机号');\n\t\t\t\t}\n\n\t\t\t\tif (!code) {\n\t\t\t\t\tthrow new Error('请输入验证码');\n\t\t\t\t}\n\n\t\t\t\t// 获取平台类型\n\t\t\t\tconst isapp = this.getPlatformType();\n\t\t\t\tconsole.log('当前登录平台类型 isapp:', isapp);\n\n\t\t\t\tconst params = {\n\t\t\t\t\tphone,\n\t\t\t\t\tcode,\n\t\t\t\t\tplatform: 2, // 用户端\n\t\t\t\t\tregistrationId: this.registerID, // 极光推送id，暂时为空\n\t\t\t\t\tisapp: isapp // 添加平台类型参数\n\t\t\t\t};\n\n\t\t\t\t// 使用API方法\n\t\t\t\tconst response = await this.$api.base.appLoginByCode(params);\n\t\t\t\tawait this.handleLoginSuccess(response);\n\t\t\t},\n\n\t\t\t// 注册\n\t\t\tasync handleRegister() {\n\t\t\t\tconst { phone, password, shortCode, pidInviteCode } = this.registerForm;\n\n\t\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\t\tthrow new Error('请输入正确的手机号');\n\t\t\t\t}\n\n\t\t\t\tif (!password) {\n\t\t\t\t\tthrow new Error('请输入密码');\n\t\t\t\t}\n\n\t\t\t\tif (password.length < 6) {\n\t\t\t\t\tthrow new Error('密码长度不能少于6位');\n\t\t\t\t}\n\n\t\t\t\tif (!shortCode) {\n\t\t\t\t\tthrow new Error('请输入验证码');\n\t\t\t\t}\n\n\t\t\t\t// 获取平台类型\n\t\t\t\tconst isapp = this.getPlatformType();\n\t\t\t\tconsole.log('当前注册平台类型 isapp:', isapp);\n\n\t\t\t\tconst params = {\n\t\t\t\t\tphone,\n\t\t\t\t\tpassword: md5(password),\n\t\t\t\t\tshortCode,\n\t\t\t\t\tpidInviteCode: pidInviteCode || '',\n\t\t\t\t\tisapp: isapp // 添加平台类型参数\n\t\t\t\t};\n\n\t\t\t\t// 使用API方法\n\t\t\t\tconst response = await this.$api.base.appRegister(params);\n\n\t\t\t\tif (response.code === '200') {\n\t\t\t\t\tthis.showToast('注册成功', 'success');\n\n\t\t\t\t\t// 检查注册响应中是否包含token（有些后端会在注册时直接返回token）\n\t\t\t\t\tconst token = this.extractTokenFromHeaders(response.header);\n\n\t\t\t\t\tif (token && response.data) {\n\t\t\t\t\t\t// 如果注册时直接返回了token和用户信息，直接处理登录成功\n\t\t\t\t\t\tconsole.log('注册时直接返回了token，跳过自动登录步骤');\n\t\t\t\t\t\tawait this.handleLoginSuccess(response);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 否则进行自动登录\n\t\t\t\t\t\tconsole.log('注册时未返回token，进行自动登录');\n\t\t\t\t\t\tthis.showToast('注册成功，正在自动登录...', 'success');\n\t\t\t\t\t\tawait this.autoLoginAfterRegister(phone, password);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(response.msg || '注册失败');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 提取token的辅助方法\n\t\t\textractTokenFromHeaders(headers) {\n\t\t\t\treturn extractTokenFromHeaders(headers);\n\t\t\t},\n\n\t\t\t// 注册成功后自动登录\n\t\t\tasync autoLoginAfterRegister(phone, password) {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取平台类型\n\t\t\t\t\tconst isapp = this.getPlatformType();\n\t\t\t\t\tconsole.log('注册后自动登录平台类型 isapp:', isapp);\n\n\t\t\t\t\tconst loginParams = {\n\t\t\t\t\t\tphone,\n\t\t\t\t\t\tpassword: md5(password),\n\t\t\t\t\t\tplatform: 2, // 用户端\n\t\t\t\t\t\tregistrationId: this.registerID, // 极光推送id，暂时为空\n\t\t\t\t\t\tisapp: isapp // 添加平台类型参数\n\t\t\t\t\t};\n\n\t\t\t\t\t// 调用登录接口\n\t\t\t\t\tconst loginResponse = await this.$api.base.appLoginByPass(loginParams);\n\n\t\t\t\t\t// 处理登录成功\n\t\t\t\t\tawait this.handleLoginSuccess(loginResponse);\n\n\t\t\t\t\tthis.showToast('登录成功', 'success');\n\n\t\t\t\t\t// 跳转到个人页面\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\turl: '/pages/mine'\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 1500);\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('自动登录失败:', error);\n\t\t\t\t\tthis.showToast('注册成功，请手动登录');\n\n\t\t\t\t\t// 自动登录失败，切换到登录模式并填充表单\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.loginForm.phone = phone;\n\t\t\t\t\t\tthis.loginForm.password = '';\n\t\t\t\t\t\tthis.currentMode = 'login';\n\t\t\t\t\t\tthis.loginType = 'password';\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 忘记密码\n\t\t\tasync handleForgotPassword() {\n\t\t\t\tconst { phone, newPassword, confirmPassword, shortCode } = this.forgotForm;\n\n\t\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\t\tthrow new Error('请输入正确的手机号');\n\t\t\t\t}\n\n\t\t\t\tif (!newPassword) {\n\t\t\t\t\tthrow new Error('请输入新密码');\n\t\t\t\t}\n\n\t\t\t\tif (newPassword.length < 6) {\n\t\t\t\t\tthrow new Error('密码长度不能少于6位');\n\t\t\t\t}\n\n\t\t\t\tif (newPassword !== confirmPassword) {\n\t\t\t\t\tthrow new Error('两次输入的密码不一致');\n\t\t\t\t}\n\n\t\t\t\tif (!shortCode) {\n\t\t\t\t\tthrow new Error('请输入验证码');\n\t\t\t\t}\n\n\t\t\t\t// 获取平台类型\n\t\t\t\tconst isapp = this.getPlatformType();\n\t\t\t\tconsole.log('当前重置密码平台类型 isapp:', isapp);\n\n\t\t\t\tconst params = {\n\t\t\t\t\tphone,\n\t\t\t\t\tnewPassword: md5(newPassword),\n\t\t\t\t\tconfirmPassword: md5(confirmPassword),\n\t\t\t\t\tshortCode,\n\t\t\t\t\tisapp: isapp // 添加平台类型参数\n\t\t\t\t};\n\n\t\t\t\t// 使用API方法\n\t\t\t\tconst response = await this.$api.base.appForgetPwd(params);\n\n\t\t\t\tif (response.code === '200') {\n\t\t\t\t\tthis.showToast('密码重置成功', 'success');\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(response.msg || '密码重置失败');\n\t\t\t\t}\n\n\t\t\t\t// 重置成功后跳转到登录\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.loginForm.phone = phone;\n\t\t\t\t\tthis.loginForm.password = '';\n\t\t\t\t\tthis.currentMode = 'login';\n\t\t\t\t\tthis.loginType = 'password';\n\t\t\t\t}, 1500);\n\t\t\t},\n\n\t\t\t// 处理登录成功\n\t\t\tasync handleLoginSuccess(response) {\n\t\t\t\tconsole.log('登录响应:', response);\n\n\t\t\t\tif (!response || response.code !== '200') {\n\t\t\t\t\tthrow new Error(response?.msg || '登录失败');\n\t\t\t\t}\n\n\t\t\t\t// 从响应头中提取token\n\t\t\t\tconst token = extractTokenFromHeaders(response.header);\n\n\t\t\t\tif (!token) {\n\t\t\t\t\tconsole.error('未找到token，响应头:', response.header);\n\t\t\t\t\tthrow new Error('登录失败，未获取到token');\n\t\t\t\t}\n\n\t\t\t\t// 保存token和用户信息\n\t\t\t\tuni.setStorageSync('token', token);\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'autograph',\n\t\t\t\t\tval: token\n\t\t\t\t});\n\n\t\t\t\t// 保存用户信息\n\t\t\t\tconst userInfo = response.data;\n\t\t\t\tconst userInfoFormatted = {\n\t\t\t\t\tphone: userInfo.phone || '',\n\t\t\t\t\tavatarUrl: userInfo.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: userInfo.nickName || '用户',\n\t\t\t\t\tuserId: userInfo.id || '',\n\t\t\t\t\tcreateTime: userInfo.createTime || '',\n\t\t\t\t\tpid: userInfo.pid || '',\n\t\t\t\t\tinviteCode: userInfo.inviteCode || ''\n\t\t\t\t};\n\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: userInfoFormatted\n\t\t\t\t});\n\n\t\t\t\t// 保存到本地存储\n\t\t\t\tthis.saveUserInfoToStorage(userInfoFormatted);\n\n\t\t\t\tthis.showToast('登录成功', 'success');\n\n\t\t\t\t// 跳转到个人页面\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\turl: '/pages/mine'\n\t\t\t\t\t});\n\t\t\t\t}, 1500);\n\t\t\t},\n\n\t\t\t// 保存用户信息到本地存储\n\t\t\tsaveUserInfoToStorage(userInfo) {\n\t\t\t\tuni.setStorageSync('phone', userInfo.phone);\n\t\t\t\tuni.setStorageSync('avatarUrl', userInfo.avatarUrl);\n\t\t\t\tuni.setStorageSync('nickName', userInfo.nickName);\n\t\t\t\tuni.setStorageSync('userId', userInfo.userId);\n\t\t\t\tuni.setStorageSync('pid', userInfo.pid);\n\t\t\t\tif (userInfo.unionid) {\n\t\t\t\t\tuni.setStorageSync('unionid', userInfo.unionid);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 微信登录\n\t\t\tasync handleWechatLogin() {\n\t\t\t\tif (this.isWechatLoading) return;\n\n\t\t\t\t// 检查是否同意协议\n\t\t\t\tif (!this.agreedToTerms) {\n\t\t\t\t\tthis.showToast('请勾选我已阅读并同意服务协议和隐私政策');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconsole.log('=== 开始APP微信登录流程 ===');\n\t\t\t\tthis.isWechatLoading = true;\n\t\t\t\tuni.showLoading({ title: '正在启动微信...' });\n\n\t\t\t\ttry {\n\t\t\t\t\t// 第一步：检查微信登录环境\n\t\t\t\t\tconsole.log('步骤1: 检查登录环境');\n\t\t\t\t\tawait this.checkWechatEnvironment();\n\n\t\t\t\t\t// 第二步：获取微信授权码\n\t\t\t\t\tconsole.log('步骤2: 获取微信授权码');\n\t\t\t\t\tuni.showLoading({ title: '正在获取授权...' });\n\t\t\t\t\tconst code = await this.getWechatCode();\n\t\t\t\t\tconsole.log('获取到授权码:', code);\n\n\t\t\t\t\t// 第三步：调用后端登录接口\n\t\t\t\t\tconsole.log('步骤3: 调用登录接口');\n\t\t\t\t\tuni.showLoading({ title: '登录中...' });\n\t\t\t\t\tawait this.callWechatLoginAPI(code);\n\n\t\t\t\t\tconsole.log('=== 微信登录流程完成 ===');\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('微信登录失败:', error);\n\n\t\t\t\t\t// 根据错误类型提供不同的提示\n\t\t\t\t\tlet errorMessage = error.message || '微信登录失败';\n\t\t\t\t\tif (errorMessage.includes('取消')) {\n\t\t\t\t\t\t// 用户主动取消，不显示错误提示\n\t\t\t\t\t\tconsole.log('用户取消微信登录');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.showToast(errorMessage);\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isWechatLoading = false;\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 检查微信登录环境\n\t\t\tasync checkWechatEnvironment() {\n\t\t\t\tconsole.log('检查微信登录环境...');\n\n\t\t\t\t// 检查当前平台\n\t\t\t\tconst platform = this.getCurrentPlatform();\n\t\t\t\tconsole.log('当前平台:', platform);\n\n\t\t\t\t// 只在APP环境下支持微信登录\n\t\t\t\tif (platform !== 'app-plus') {\n\t\t\t\t\tif (platform === 'h5') {\n\t\t\t\t\t\tthrow new Error('H5环境不支持微信登录，请下载APP使用');\n\t\t\t\t\t} else if (platform === 'mp-weixin') {\n\t\t\t\t\t\tthrow new Error('请使用小程序原生登录方式');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error('当前环境不支持微信登录，请在APP中使用');\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 检查设备是否安装微信\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tconsole.log('开始检查微信安装状态...');\n\t\t\t\ttry {\n\t\t\t\t\tconst isWechatInstalled = await new Promise((resolve) => {\n\t\t\t\t\t\tconsole.log('调用plus.runtime.isApplicationExist检查微信...');\n\n\t\t\t\t\t\t// 添加超时处理\n\t\t\t\t\t\tconst timeout = setTimeout(() => {\n\t\t\t\t\t\t\tconsole.warn('检查微信安装状态超时，跳过此检查');\n\t\t\t\t\t\t\tresolve(true); // 超时时假设已安装，继续流程\n\t\t\t\t\t\t}, 5000); // 5秒超时\n\n\t\t\t\t\t\tplus.runtime.isApplicationExist({\n\t\t\t\t\t\t\tpname: 'com.tencent.mm', // Android微信包名\n\t\t\t\t\t\t\taction: 'weixin://' // iOS微信scheme\n\t\t\t\t\t\t}, (result) => {\n\t\t\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\t\t\tconsole.log('微信安装检查结果:', result);\n\t\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\tconsole.log('微信安装状态检查完成:', isWechatInstalled);\n\t\t\t\t\tif (!isWechatInstalled) {\n\t\t\t\t\t\tthrow new Error('设备未安装微信，请先安装微信客户端');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.warn('检查微信安装状态失败:', error);\n\t\t\t\t\t// 不阻止登录流程，继续尝试\n\t\t\t\t}\n\t\t\t\t// #endif\n\n\t\t\t\t// 检查OAuth服务提供商\n\t\t\t\tconsole.log('开始检查OAuth服务提供商...');\n\t\t\t\ttry {\n\t\t\t\t\tconst providers = await new Promise((resolve, reject) => {\n\t\t\t\t\t\tconsole.log('调用uni.getProvider获取OAuth服务...');\n\n\t\t\t\t\t\t// 添加超时处理\n\t\t\t\t\t\tconst timeout = setTimeout(() => {\n\t\t\t\t\t\t\tconsole.error('获取OAuth服务超时');\n\t\t\t\t\t\t\treject(new Error('获取OAuth服务超时，请检查网络连接'));\n\t\t\t\t\t\t}, 10000); // 10秒超时\n\n\t\t\t\t\t\tuni.getProvider({\n\t\t\t\t\t\t\tservice: 'oauth',\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\t\t\t\tconsole.log('获取OAuth服务成功:', res);\n\t\t\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\t\t\t\tconsole.error('获取OAuth服务失败:', err);\n\t\t\t\t\t\t\t\treject(err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\tconsole.log('OAuth提供商:', providers);\n\n\t\t\t\t\tif (!providers.provider || !providers.provider.includes('weixin')) {\n\t\t\t\t\t\tthrow new Error('微信登录服务未配置，请联系开发者');\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log('OAuth服务检查完成，微信登录服务可用');\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取OAuth服务失败:', error);\n\t\t\t\t\tthrow new Error('获取登录服务失败，请检查网络连接或重启APP');\n\t\t\t\t}\n\n\t\t\t\tconsole.log('微信登录环境检查完成');\n\t\t\t},\n\n\t\t\t// 获取微信授权码\n\t\t\tasync getWechatCode() {\n\t\t\t\tconsole.log('获取微信授权码...');\n\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tconsole.log('开始调用uni.login获取微信授权码...');\n\n\t\t\t\t\t// 设置超时\n\t\t\t\t\tconst timeout = setTimeout(() => {\n\t\t\t\t\t\tconsole.error('微信授权超时，30秒内未收到响应');\n\t\t\t\t\t\treject(new Error('微信授权超时，请重试'));\n\t\t\t\t\t}, 30000); // 30秒超时\n\n\t\t\t\t\tconsole.log('调用uni.login，参数:', {\n\t\t\t\t\t\tprovider: \"weixin\",\n\t\t\t\t\t\tonlyAuthorize: true\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.login({\n\t\t\t\t\t\t\"provider\": \"weixin\",\n\t\t\t\t\t\t\t\"onlyAuthorize\": true, // 微信登录仅请求授权认证\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\t\t\tconsole.log('获取微信code成功:', res);\n\n\n\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\tconsole.log('微信授权码获取成功，code:', res.code);\n\t\t\t\t\t\t\t\tresolve(res.code);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.error('微信返回结果中没有code字段:', res);\n\t\t\t\t\t\t\t\treject(new Error('微信返回的授权码为空'));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\t\t\tconsole.error('获取微信code失败:', err);\n\n\t\t\t\t\t\t\t// APP环境下的错误处理\n\t\t\t\t\t\t\tlet errorMsg = '微信授权失败';\n\n\t\t\t\t\t\t\t// 根据错误码处理\n\t\t\t\t\t\t\tif (err.code) {\n\t\t\t\t\t\t\t\tswitch (err.code) {\n\t\t\t\t\t\t\t\t\tcase 1000:\n\t\t\t\t\t\t\t\t\t\terrorMsg = '用户取消了微信授权';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 1001:\n\t\t\t\t\t\t\t\t\t\terrorMsg = '微信授权被拒绝';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 1002:\n\t\t\t\t\t\t\t\t\t\terrorMsg = '网络错误，请检查网络连接';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 1003:\n\t\t\t\t\t\t\t\t\t\terrorMsg = '用户点击了拒绝按钮';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 1004:\n\t\t\t\t\t\t\t\t\t\terrorMsg = '应用未安装微信';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase 1005:\n\t\t\t\t\t\t\t\t\t\terrorMsg = '微信版本过低，请更新微信';\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\terrorMsg = `微信授权失败 (错误码: ${err.code})`;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if (err.errMsg) {\n\t\t\t\t\t\t\t\terrorMsg = err.errMsg;\n\t\t\t\t\t\t\t} else if (err.message) {\n\t\t\t\t\t\t\t\terrorMsg = err.message;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\treject(new Error(errorMsg));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 获取微信用户信息\n\t\t\tasync getWechatUserInfo() {\n\t\t\t\tconsole.log('获取微信用户信息...');\n\n\t\t\t\t// 在APP环境下，用户信息获取是可选的\n\t\t\t\t// 很多情况下只需要code就可以完成登录\n\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\t// 设置较短的超时时间，因为这不是必需的\n\t\t\t\t\tconst timeout = setTimeout(() => {\n\t\t\t\t\t\tconsole.log('获取用户信息超时，使用空数据继续');\n\t\t\t\t\t\tresolve({\n\t\t\t\t\t\t\tencryptedData: '',\n\t\t\t\t\t\t\tiv: ''\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 10000); // 10秒超时\n\n\t\t\t\t\tuni.getUserInfo({\n\t\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\t\t\tconsole.log('获取用户信息成功:', res);\n\t\t\t\t\t\t\tresolve({\n\t\t\t\t\t\t\t\tencryptedData: res.encryptedData || '',\n\t\t\t\t\t\t\t\tiv: res.iv || ''\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\t\t\tconsole.warn('获取用户信息失败，继续登录流程:', err);\n\t\t\t\t\t\t\t// 在APP环境下，用户信息获取失败是常见的，不影响登录\n\t\t\t\t\t\t\tresolve({\n\t\t\t\t\t\t\t\tencryptedData: '',\n\t\t\t\t\t\t\t\tiv: ''\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 调用后端微信登录接口\n\t\t\tasync callWechatLoginAPI(code) {\n\t\t\t\tconsole.log('调用后端微信登录接口...');\n\n\t\t\t\t// 获取极光推送ID\n\t\t\t\t// const registrationId = await this.getRegistrationId();\n\t\t\t\t// console.log('极光推送ID:', registrationId);\n\n\t\t\t\tconst params = {\n\t\t\t\t\tcode: code,\n\t\t\t\t\tplatform: 2, // 2表示用户端，1表示师傅端\n\t\t\t\t\tregistrationId:  this.registerID\n\t\t\t\t};\n\n\t\t\t\tconsole.log('登录参数:', params);\n\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await this.$api.base.appLoginByWechat(params);\n\t\t\t\t\tconsole.log('登录接口响应:', response);\n\n\t\t\t\t\tif (response && response.code === '200') {\n\t\t\t\t\t\tawait this.handleWechatLoginSuccess(response);\n\t\t\t\t\t\tthis.showToast('微信登录成功', 'success');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(response?.msg || '登录失败，请重试');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('调用登录接口失败:', error);\n\t\t\t\t\tthrow new Error('网络请求失败，请检查网络连接');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 处理微信登录成功 - 新方法专门处理用户数据\n\t\t\tasync handleWechatLoginSuccess(response) {\n\t\t\t\tconsole.log('=== 开始处理微信登录成功响应 ===');\n\t\t\t\tconsole.log('微信登录响应:', response);\n\n\t\t\t\tif (!response || response.code !== '200') {\n\t\t\t\t\tthrow new Error(response?.msg || '微信登录失败');\n\t\t\t\t}\n\n\t\t\t\t// 微信登录返回的数据结构：\n\t\t\t\t// code: \"200\"\n\t\t\t\t// data: { 用户信息对象 }\n\t\t\t\t// header: {...}\n\n\t\t\t\t// 从响应中获取用户数据\n\t\t\t\tconst userData = response.data;\n\t\t\t\tconsole.log('微信登录获取到的用户数据:', userData);\n\n\t\t\t\tif (!userData || !userData.id) {\n\t\t\t\t\tconsole.error('微信登录响应中未找到用户数据');\n\t\t\t\t\tthrow new Error('微信登录失败，未获取到用户数据');\n\t\t\t\t}\n\n\t\t\t\t// 从响应头中提取token（如果有的话）\n\t\t\t\tconst token = this.extractTokenFromHeaders(response.header);\n\t\t\t\tconsole.log('从响应头提取的token:', token);\n\n\t\t\t\tif (token) {\n\t\t\t\t\t// 保存token\n\t\t\t\t\tuni.setStorageSync('token', token);\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'autograph',\n\t\t\t\t\t\tval: token\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('微信登录token已保存');\n\t\t\t\t}\n\n\t\t\t\t// 格式化用户信息\n\t\t\t\tconst userInfoFormatted = {\n\t\t\t\t\tphone: userData.phone || '',\n\t\t\t\t\tavatarUrl: userData.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: userData.nickName || '微信用户',\n\t\t\t\t\tuserId: userData.id || '',\n\t\t\t\t\tcreateTime: userData.createTime || '',\n\t\t\t\t\tpid: userData.pid || '',\n\t\t\t\t\tinviteCode: userData.inviteCode || '',\n\t\t\t\t\tunionid: userData.unionid || '', // 保存unionid用于后续绑定手机号\n\t\t\t\t\tappOpenid: userData.appOpenid || '' // 保存appOpenid\n\t\t\t\t};\n\n\t\t\t\t// 保存用户信息到Vuex\n\t\t\t\tconsole.log('保存用户信息到Vuex:', userInfoFormatted);\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: userInfoFormatted\n\t\t\t\t});\n\n\t\t\t\t// 保存到本地存储\n\t\t\t\tconsole.log('保存用户信息到本地存储');\n\t\t\t\tthis.saveUserInfoToStorage(userInfoFormatted);\n\n\t\t\t\t// 额外保存unionid到本地存储，用于绑定手机号\n\t\t\t\tif (userData.unionid) {\n\t\t\t\t\tconsole.log('保存unionid到本地存储:', userData.unionid);\n\t\t\t\t\tuni.setStorageSync('unionid', userData.unionid);\n\t\t\t\t}\n\n\t\t\t\t// 验证保存结果\n\t\t\t\tconsole.log('验证保存结果:');\n\t\t\t\tconsole.log('本地存储userId:', uni.getStorageSync('userId'));\n\t\t\t\tconsole.log('本地存储nickName:', uni.getStorageSync('nickName'));\n\t\t\t\tconsole.log('本地存储unionid:', uni.getStorageSync('unionid'));\n\t\t\t\tconsole.log('Vuex中的token:', this.$store.state.user.autograph);\n\n\t\t\t\tconsole.log('微信用户信息已保存:', userInfoFormatted);\n\t\t\t\tconsole.log('=== 微信登录成功处理完成 ===');\n\n\t\t\t\t// 跳转到个人页面\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\turl: '/pages/mine'\n\t\t\t\t\t});\n\t\t\t\t}, 1500);\n\t\t\t},\n\n\t\t\t// 获取极光推送ID\n\t\t\tasync getRegistrationId() {\n\t\t\t\ttry {\n\t\t\t\t\t// 尝试从存储中获取\n\t\t\t\t\tconst storedId = uni.getStorageSync('registrationId');\n\t\t\t\t\tif (storedId) {\n\t\t\t\t\t\tconsole.log('从存储中获取到极光推送ID:', storedId);\n\t\t\t\t\t\treturn storedId;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 如果存储中没有，尝试从极光推送插件获取\n\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\tif (typeof plus !== 'undefined' && plus.push) {\n\t\t\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\t\t\tplus.push.getClientInfo((info) => {\n\t\t\t\t\t\t\t\tconsole.log('从极光推送获取到ID:', info.clientid);\n\t\t\t\t\t\t\t\tif (info.clientid) {\n\t\t\t\t\t\t\t\t\tuni.setStorageSync('registrationId', info.clientid);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tresolve(info.clientid || '');\n\t\t\t\t\t\t\t}, (error) => {\n\t\t\t\t\t\t\t\tconsole.warn('获取极光推送ID失败:', error);\n\t\t\t\t\t\t\t\tresolve('');\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\t// #endif\n\n\t\t\t\t\tconsole.log('无法获取极光推送ID，返回空字符串');\n\t\t\t\t\treturn '';\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.warn('获取极光推送ID异常:', error);\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取邀请码\n\t\t\tgetInviteCode() {\n\t\t\t\t// 可以从页面参数、存储等地方获取邀请码\n\t\t\t\treturn this.registerForm.pidInviteCode || '';\n\t\t\t},\n\n\t\t\t// 获取密码强度\n\t\t\tgetPasswordStrength() {\n\t\t\t\tconst password = this.registerForm.password;\n\t\t\t\tif (!password) return 0;\n\n\t\t\t\tlet strength = 0;\n\n\t\t\t\t// 长度检查\n\t\t\t\tif (password.length >= 6) strength++;\n\n\t\t\t\t// 包含数字和字母\n\t\t\t\tif (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++;\n\n\t\t\t\t// 包含特殊字符或长度超过8位\n\t\t\t\tif (/[^a-zA-Z0-9]/.test(password) || password.length >= 8) strength++;\n\n\t\t\t\treturn strength;\n\t\t\t},\n\n\t\t\t// 获取密码强度文本\n\t\t\tgetPasswordStrengthText() {\n\t\t\t\tconst strength = this.getPasswordStrength();\n\t\t\t\tswitch (strength) {\n\t\t\t\t\tcase 1: return '弱';\n\t\t\t\t\tcase 2: return '中';\n\t\t\t\t\tcase 3: return '强';\n\t\t\t\t\tdefault: return '';\n\t\t\t\t}\n\t\t\t},\n\n\n\n\t\t\t// 显示提示信息\n\t\t\tshowToast(title, icon = 'none') {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle,\n\t\t\t\t\ticon,\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t.login-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t.bg-decoration {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tpointer-events: none;\n\n\t\t.circle {\n\t\t\tposition: absolute;\n\t\t\tborder-radius: 50%;\n\t\t\tbackground: rgba(59, 130, 246, 0.08);\n\t\t\tanimation: float 8s ease-in-out infinite;\n\n\t\t\t&.circle-1 {\n\t\t\t\twidth: 200rpx;\n\t\t\t\theight: 200rpx;\n\t\t\t\ttop: 10%;\n\t\t\t\tright: -50rpx;\n\t\t\t\tanimation-delay: 0s;\n\t\t\t}\n\n\t\t\t&.circle-2 {\n\t\t\t\twidth: 150rpx;\n\t\t\t\theight: 150rpx;\n\t\t\t\ttop: 60%;\n\t\t\t\tleft: -30rpx;\n\t\t\t\tanimation-delay: 3s;\n\t\t\t}\n\n\t\t\t&.circle-3 {\n\t\t\t\twidth: 100rpx;\n\t\t\t\theight: 100rpx;\n\t\t\t\ttop: 30%;\n\t\t\t\tleft: 50%;\n\t\t\t\tanimation-delay: 6s;\n\t\t\t}\n\t\t}\n\t}\n\n\t@keyframes float {\n\t\t0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }\n\t\t50% { transform: translateY(-15px) rotate(180deg); opacity: 0.3; }\n\t}\n\n\t.header {\n\t\tpadding: 80rpx 40rpx 40rpx;\n\t\tposition: relative;\n\t\tz-index: 10;\n\n\t\t.back-btn {\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tbackground: rgba(59, 130, 246, 0.1);\n\t\t\tbackdrop-filter: blur(10rpx);\n\t\t\tborder-radius: 50%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\ttransition: all 0.3s ease;\n\t\t\tborder: 1rpx solid rgba(59, 130, 246, 0.2);\n\n\t\t\t&:active {\n\t\t\t\tbackground: rgba(59, 130, 246, 0.15);\n\t\t\t\ttransform: scale(0.95);\n\t\t\t}\n\t\t}\n\t}\n\n\t.logo-section {\n\t\ttext-align: center;\n\t\tpadding: 0 40rpx;\n\t\tmargin-bottom: 60rpx;\n\t\tposition: relative;\n\t\tz-index: 10;\n\n\t\t.logo-wrapper {\n\t\t\tposition: relative;\n\t\t\tdisplay: inline-block;\n\t\t\tmargin-bottom: 32rpx;\n\n\t\t\t&::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: -10rpx;\n\t\t\t\tleft: -10rpx;\n\t\t\t\tright: -10rpx;\n\t\t\t\tbottom: -10rpx;\n\t\t\t\tbackground: linear-gradient(45deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05));\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tanimation: pulse 3s ease-in-out infinite;\n\t\t\t}\n\n\t\t\t.logo {\n\t\t\t\twidth: 100rpx;\n\t\t\t\theight: 100rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 1;\n\t\t\t\tbox-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.15);\n\t\t\t}\n\t\t}\n\n\t\t.app-name {\n\t\t\tfont-size: 52rpx;\n\t\t\tfont-weight: 700;\n\t\t\tcolor: #1e293b;\n\t\t\tmargin-bottom: 16rpx;\n\t\t\ttext-shadow: none;\n\t\t}\n\n\t\t.welcome-text {\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #64748b;\n\t\t\tline-height: 1.5;\n\t\t\tfont-weight: 400;\n\t\t}\n\t}\n\n\t@keyframes pulse {\n\t\t0%, 100% { transform: scale(1); opacity: 0.8; }\n\t\t50% { transform: scale(1.05); opacity: 0.4; }\n\t}\n\n\t.main-card {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(20rpx);\n\t\tborder-radius: 32rpx 32rpx 0 0;\n\t\tmargin: 0 20rpx;\n\t\tpadding: 60rpx 40rpx 40rpx;\n\t\tbox-shadow: 0 -8rpx 32rpx rgba(59, 130, 246, 0.08);\n\t\tposition: relative;\n\t\tz-index: 10;\n\t\tmin-height: calc(100vh - 400rpx);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.8);\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: 20rpx;\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t\twidth: 60rpx;\n\t\t\theight: 6rpx;\n\t\t\tbackground: #cbd5e1;\n\t\t\tborder-radius: 3rpx;\n\t\t}\n\n\t\t.mode-title {\n\t\t\ttext-align: center;\n\t\t\tfont-size: 48rpx;\n\t\t\tfont-weight: 700;\n\t\t\tcolor: #1e293b;\n\t\t\tmargin-bottom: 60rpx;\n\t\t\tposition: relative;\n\n\t\t\t&::after {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: -20rpx;\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translateX(-50%);\n\t\t\t\twidth: 80rpx;\n\t\t\t\theight: 4rpx;\n\t\t\t\tbackground: linear-gradient(90deg, #3b82f6, #1d4ed8);\n\t\t\t\tborder-radius: 2rpx;\n\t\t\t}\n\t\t}\n\n\t\t.form-content {\n\t\t\tmargin-top: 40rpx;\n\t\t}\n\t}\n\n\t.tab-switcher {\n\t\tdisplay: flex;\n\t\tbackground: #f1f5f9;\n\t\tborder-radius: 16rpx;\n\t\tmargin-bottom: 50rpx;\n\t\tpadding: 6rpx;\n\t\tposition: relative;\n\t\tbox-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);\n\n\t\t.tab-item {\n\t\t\tflex: 1;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tpadding: 20rpx 16rpx;\n\t\t\tborder-radius: 12rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #64748b;\n\t\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\t\tposition: relative;\n\t\t\tcursor: pointer;\n\n\t\t\ttext {\n\t\t\t\tmargin-left: 8rpx;\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\tbackground: linear-gradient(135deg, #3b82f6, #1d4ed8);\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);\n\t\t\t\ttransform: translateY(-1rpx);\n\n\t\t\t\ttext {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:not(.active):active {\n\t\t\t\ttransform: scale(0.98);\n\t\t\t}\n\t\t}\n\t}\n\n\t// 注册步骤指示器\n\t.register-steps {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 40rpx;\n\t\tpadding: 0 20rpx;\n\n\t\t.step-item {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tposition: relative;\n\n\t\t\t.step-number {\n\t\t\t\twidth: 60rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tbackground: #e0e0e0;\n\t\t\t\tcolor: #999;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t&.active {\n\t\t\t\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\ttransform: scale(1.1);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.step-text {\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #999;\n\t\t\t\ttransition: color 0.3s ease;\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\t.step-number {\n\t\t\t\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\n\t\t\t\t.step-text {\n\t\t\t\t\tcolor: #667eea;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.step-line {\n\t\t\twidth: 80rpx;\n\t\t\theight: 4rpx;\n\t\t\tbackground: #e0e0e0;\n\t\t\tmargin: 0 20rpx;\n\t\t\tborder-radius: 2rpx;\n\t\t\tposition: relative;\n\t\t\ttop: -30rpx;\n\n\t\t\t&.active {\n\t\t\t\tbackground: linear-gradient(90deg, #667eea, #764ba2);\n\t\t\t}\n\t\t}\n\t}\n\n\t// 注册提示\n\t.register-tip {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: rgba(102, 126, 234, 0.05);\n\t\tborder: 1rpx solid rgba(102, 126, 234, 0.2);\n\t\tborder-radius: 16rpx;\n\t\tpadding: 24rpx;\n\t\tmargin-bottom: 40rpx;\n\n\t\ttext {\n\t\t\tmargin-left: 16rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #667eea;\n\t\t\tline-height: 1.4;\n\t\t}\n\t}\n\n\t.input-group {\n\t\t.input-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tbackground: #f8fafc;\n\t\t\tborder: 2rpx solid #e2e8f0;\n\t\t\tborder-radius: 16rpx;\n\t\t\tmargin-bottom: 32rpx;\n\t\t\tmin-height: 110rpx;\n\t\t\tpadding: 0;\n\t\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\n\t\t\t&.optional {\n\t\t\t\tborder-style: dashed;\n\t\t\t\tborder-color: #cbd5e1;\n\t\t\t\tbackground: #f8fafc;\n\t\t\t}\n\n\t\t\t&::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tbackground: linear-gradient(135deg, rgba(59, 130, 246, 0.03), rgba(29, 78, 216, 0.03));\n\t\t\t\topacity: 0;\n\t\t\t\ttransition: opacity 0.3s ease;\n\t\t\t}\n\n\t\t\t&:focus-within {\n\t\t\t\tborder-color: #3b82f6;\n\t\t\t\tbox-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.08);\n\t\t\t\ttransform: translateY(-1rpx);\n\n\t\t\t\t&::before {\n\t\t\t\t\topacity: 1;\n\t\t\t\t}\n\n\t\t\t\t.input-icon {\n\t\t\t\t\ttransform: scale(1.05);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.input-icon {\n\t\t\t\twidth: 50rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-left: 24rpx;\n\t\t\t\tbackground: rgba(59, 130, 246, 0.08);\n\t\t\t\tborder-radius: 50%;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\n\t\t\t.input-content {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-left: 24rpx;\n\t\t\t\tmargin-right: 16rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.input-label {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #64748b;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.optional-tag {\n\t\t\t\t\t\tbackground: #f59e0b;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tpadding: 2rpx 8rpx;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tmargin-left: 12rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.input-field {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #1e293b;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tbackground: transparent;\n\t\t\t\t\tborder: none;\n\t\t\t\t\toutline: none;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\tpadding: 4rpx 0;\n\n\t\t\t\t\t&::placeholder {\n\t\t\t\t\t\tcolor: #94a3b8;\n\t\t\t\t\t\tfont-weight: 300;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 直接在input-item下的input-field（没有input-content包装的）\n\t\t\t> .input-field {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-left: 24rpx;\n\t\t\t\tmargin-right: 16rpx;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #1e293b;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tbackground: transparent;\n\t\t\t\tborder: none;\n\t\t\t\toutline: none;\n\t\t\t\tline-height: 1.4;\n\t\t\t\tpadding: 0;\n\n\t\t\t\t&::placeholder {\n\t\t\t\t\tcolor: #94a3b8;\n\t\t\t\t\tfont-weight: 300;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.input-status {\n\t\t\t\twidth: 50rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-right: 24rpx;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\n\t\t\t.action-icon {\n\t\t\t\twidth: 50rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-right: 24rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\ttransition: all 0.2s ease;\n\t\t\t\tflex-shrink: 0;\n\n\t\t\t\t&:active {\n\t\t\t\t\tbackground: rgba(59, 130, 246, 0.08);\n\t\t\t\t\ttransform: scale(0.9);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.input-benefit {\n\t\t\t\twidth: 50rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-right: 24rpx;\n\t\t\t\tbackground: rgba(255, 149, 0, 0.1);\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\n\t\t\t.sms-btn {\n\t\t\t\tbackground: linear-gradient(135deg, #3b82f6, #1d4ed8);\n\t\t\t\tcolor: #fff;\n\t\t\t\tpadding: 16rpx 24rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tmargin-right: 16rpx;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);\n\t\t\t\tflex-shrink: 0;\n\n\t\t\t\t&.disabled {\n\t\t\t\t\tbackground: #cbd5e1;\n\t\t\t\t\tcolor: #64748b;\n\t\t\t\t\tbox-shadow: none;\n\t\t\t\t}\n\n\t\t\t\t&:not(.disabled):active {\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\tbox-shadow: 0 1rpx 4rpx rgba(59, 130, 246, 0.3);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// 密码强度指示器\n\t.password-strength {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin: -16rpx 0 32rpx 98rpx;\n\t\tpadding: 16rpx 24rpx;\n\t\tbackground: rgba(59, 130, 246, 0.04);\n\t\tborder-radius: 12rpx;\n\n\t\t.strength-label {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #64748b;\n\t\t\tmargin-right: 16rpx;\n\t\t}\n\n\t\t.strength-bar {\n\t\t\tdisplay: flex;\n\t\t\tmargin-right: 16rpx;\n\n\t\t\t.strength-item {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 6rpx;\n\t\t\t\tbackground: #e0e0e0;\n\t\t\t\tborder-radius: 3rpx;\n\t\t\t\tmargin-right: 8rpx;\n\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t&.active {\n\t\t\t\t\t&:nth-child(1) {\n\t\t\t\t\t\tbackground: #ff4d4f; // 弱 - 红色\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-child(2) {\n\t\t\t\t\t\tbackground: #faad14; // 中 - 橙色\n\t\t\t\t\t}\n\t\t\t\t\t&:nth-child(3) {\n\t\t\t\t\t\tbackground: #52c41a; // 强 - 绿色\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.strength-text {\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #3b82f6;\n\t\t}\n\t}\n\n\t// 注册优势展示\n\t.register-benefits {\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\tmargin-top: 40rpx;\n\t\tpadding: 32rpx 20rpx;\n\t\tbackground: linear-gradient(135deg, rgba(59, 130, 246, 0.04), rgba(29, 78, 216, 0.04));\n\t\tborder-radius: 16rpx;\n\t\tborder: 1rpx solid rgba(59, 130, 246, 0.08);\n\n\t\t.benefit-item {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tflex: 1;\n\n\t\t\ttext {\n\t\t\t\tfont-size: 22rpx;\n\t\t\t\tcolor: #64748b;\n\t\t\t\tmargin-top: 12rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tline-height: 1.3;\n\t\t\t}\n\t\t}\n\t}\n\n\t// 登录页面的链接区域\n\t.login-links {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-top: 24rpx;\n\t\tpadding: 0 8rpx;\n\n\t\t.forgot-link {\n\t\t\tcolor: #3b82f6;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\ttransition: all 0.2s ease;\n\n\t\t\t&:active {\n\t\t\t\tcolor: #1d4ed8;\n\t\t\t\ttransform: scale(0.98);\n\t\t\t}\n\t\t}\n\n\t\t.register-link {\n\t\t\tcolor: #64748b;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\ttransition: all 0.2s ease;\n\n\t\t\t.link-highlight {\n\t\t\t\tcolor: #3b82f6;\n\t\t\t\tfont-weight: 600;\n\t\t\t\ttransition: all 0.2s ease;\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.98);\n\n\t\t\t\t.link-highlight {\n\t\t\t\t\tcolor: #1d4ed8;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.agreement-section {\n\t\tmargin-bottom: 50rpx;\n\t\tpadding: 0 20rpx;\n\n\t\t.checkbox-container {\n\t\t\tdisplay: flex;\n\t\t\talign-items: flex-start;\n\n\t\t\t.checkbox {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tborder: 2rpx solid #cbd5e1;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-right: 24rpx;\n\t\t\t\tmargin-top: 2rpx;\n\t\t\t\tflex-shrink: 0;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tbackground: #fff;\n\n\t\t\t\t&.checked {\n\t\t\t\t\tbackground: linear-gradient(135deg, #3b82f6, #1d4ed8);\n\t\t\t\t\tborder-color: #3b82f6;\n\t\t\t\t\ttransform: scale(1.05);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.agreement-text {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #64748b;\n\t\t\t\tline-height: 1.6;\n\t\t\t\tflex: 1;\n\n\t\t\t\t.link {\n\t\t\t\t\tcolor: #3b82f6;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\ttransition: all 0.2s ease;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tcolor: #1d4ed8;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.wechat-login-button {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: linear-gradient(135deg, #1AAD19, #169917);\n\t\tborder-radius: 44rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: #fff;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 32rpx;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(26, 173, 25, 0.25);\n\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: -100%;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);\n\t\t\ttransition: left 0.5s ease;\n\t\t}\n\n\t\t&:active:not(.disabled) {\n\t\t\ttransform: translateY(2rpx) scale(0.98);\n\t\t\tbox-shadow: 0 3rpx 12rpx rgba(26, 173, 25, 0.35);\n\n\t\t\t&::before {\n\t\t\t\tleft: 100%;\n\t\t\t}\n\t\t}\n\n\t\t&.disabled {\n\t\t\tbackground: #cbd5e1;\n\t\t\tcolor: #64748b;\n\t\t\tbox-shadow: none;\n\t\t\ttransform: none;\n\n\t\t\t&::before {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\n\t\t.loading-icon {\n\t\t\tmargin-right: 12rpx;\n\t\t\tanimation: spin 1s linear infinite;\n\t\t}\n\n\t\t.wechat-icon {\n\t\t\twidth: 44rpx;\n\t\t\theight: 44rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin-right: 16rpx;\n\t\t\tbackground: rgba(255, 255, 255, 0.15);\n\t\t\tborder-radius: 50%;\n\t\t}\n\n\t\ttext {\n\t\t\tposition: relative;\n\t\t\tz-index: 1;\n\t\t}\n\t}\n\n\n\n\t.action-button {\n\t\twidth: 100%;\n\t\theight: 96rpx;\n\t\tbackground: linear-gradient(135deg, #3b82f6, #1d4ed8);\n\t\tborder-radius: 48rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: #fff;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tmargin: 32rpx 0;\n\t\tbox-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.25);\n\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: -100%;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);\n\t\t\ttransition: left 0.5s ease;\n\t\t}\n\n\t\t&:active {\n\t\t\ttransform: translateY(2rpx) scale(0.98);\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.35);\n\n\t\t\t&::before {\n\t\t\t\tleft: 100%;\n\t\t\t}\n\t\t}\n\n\t\t&.disabled {\n\t\t\tbackground: #cbd5e1;\n\t\t\tcolor: #64748b;\n\t\t\tbox-shadow: none;\n\t\t\ttransform: none;\n\n\t\t\t&::before {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\n\t\t.loading-icon {\n\t\t\tmargin-right: 12rpx;\n\t\t\tanimation: spin 1s linear infinite;\n\t\t}\n\n\t\ttext {\n\t\t\tposition: relative;\n\t\t\tz-index: 1;\n\t\t}\n\t}\n\n\t@keyframes spin {\n\t\tfrom { transform: rotate(0deg); }\n\t\tto { transform: rotate(360deg); }\n\t}\n\n\t.switch-links {\n\t\ttext-align: center;\n\t\tmargin-top: 40rpx;\n\t\tpadding-bottom: 40rpx;\n\n\t\t.link-text {\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #64748b;\n\t\t\tfont-weight: 400;\n\t\t\ttransition: all 0.2s ease;\n\n\t\t\t.link-highlight {\n\t\t\t\tcolor: #3b82f6;\n\t\t\t\tfont-weight: 600;\n\t\t\t\ttransition: all 0.2s ease;\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.98);\n\n\t\t\t\t.link-highlight {\n\t\t\t\t\tcolor: #1d4ed8;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.bottom-decoration {\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 100rpx;\n\t\toverflow: hidden;\n\t\tpointer-events: none;\n\n\t\t.wave {\n\t\t\tposition: absolute;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 200%;\n\t\t\theight: 100rpx;\n\t\t\tbackground: linear-gradient(135deg, rgba(59, 130, 246, 0.06), rgba(29, 78, 216, 0.06));\n\t\t\tborder-radius: 50% 50% 0 0;\n\t\t\tanimation: wave 4s ease-in-out infinite;\n\t\t}\n\t}\n\n\t@keyframes wave {\n\t\t0%, 100% { transform: translateX(-50%) translateY(0); }\n\t\t50% { transform: translateX(-50%) translateY(-8rpx); }\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&id=18804380&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754710513485\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}