# 隐私协议配置说明

## 配置完成情况

### ✅ 已完成的配置

1. **androidPrivacy.json 文件**
   - 已从 `APP用户端` 项目复制到 `APP师傅端` 根目录
   - 包含隐私协议弹窗的完整配置
   - 协议链接指向: `https://zskj.asia/protocol.html`
   - 修正了 `hrefLoader` 配置为 `"system"`

2. **manifest.json 配置更新**
   - 添加了 `"checkPermissionDenied" : true` 配置
   - 添加了 `"useOriginalMsgbox" : true` 配置
   - **重要**: 添加了 `"privacy": {"prompt": "template"}` 配置
   - 这些配置对于隐私协议正常显示至关重要

## 隐私协议配置详情

### androidPrivacy.json 文件内容
```json
{
  "version": "1",  
  "prompt": "template",
  "title": "服务协议和隐私政策",
  "message": "请你务必审慎阅读、充分理解"服务协议"和"隐私政策"各条款...",
  "buttonAccept": "同意并接受",
  "buttonRefuse": "暂不同意",
  "hrefLoader": "system|default",
  "backToExit":"false",
  "second": {
    "title": "确认提示",
    "message": "进入应用前，你需先同意《服务协议》和《隐私政策》，否则将退出应用。",
    "buttonAccept": "同意并继续",
    "buttonRefuse": "退出应用"
  },
  "disagreeMode":{
    "support": false,
    "loadNativePlugins": false,
    "visitorEntry": false,
    "showAlways": false 
  }
}
```

### manifest.json 关键配置
```json
"app-plus" : {
    "checkPermissionDenied" : true,
    "privacy" : {
        "prompt" : "template"
    },
    // ... 其他配置
    "distribute" : {
        // ... 其他配置
        "splashscreen" : {
            "useOriginalMsgbox" : true
        }
    }
}
```

## 功能说明

### 1. 隐私协议弹窗流程
1. **首次启动**: APP 启动时会自动显示隐私协议弹窗
2. **用户选择**: 用户可以选择"同意并接受"或"暂不同意"
3. **二次确认**: 如果用户选择"暂不同意"，会显示二次确认弹窗
4. **最终决定**: 用户必须同意才能继续使用应用，否则应用退出

### 2. 配置参数说明
- `version`: 协议版本号
- `prompt`: 弹窗类型，"template" 表示使用模板样式
- `title`: 弹窗标题
- `message`: 弹窗内容，支持 HTML 格式
- `buttonAccept`: 同意按钮文本
- `buttonRefuse`: 拒绝按钮文本
- `hrefLoader`: 链接打开方式
- `backToExit`: 是否允许返回键退出
- `second`: 二次确认弹窗配置
- `disagreeMode`: 不同意模式的相关设置

## 测试方法

### 1. 清除应用数据
为了测试隐私协议弹窗，需要清除应用的本地数据：
- Android: 在设置中清除应用数据
- iOS: 卸载并重新安装应用

### 2. 重新启动应用
清除数据后重新启动应用，应该会看到隐私协议弹窗

### 3. 测试场景
1. **正常同意**: 点击"同意并接受"，应用正常启动
2. **拒绝测试**: 点击"暂不同意"，查看二次确认弹窗
3. **最终拒绝**: 在二次确认中点击"退出应用"，应用应该退出

## 重要注意事项

### ⚠️ 关键要求
1. **必须使用 HBuilderX 3.2.15+ 版本打包**
   - 官网明确要求使用此版本或更高版本
   - 否则无法正常上架应用市场

2. **必须使用 template 配置隐私弹窗**
   - 在 `manifest.json` 中配置 `"privacy": {"prompt": "template"}`
   - 在 `androidPrivacy.json` 中配置 `"prompt": "template"`

3. **必须使用自定义调试基座**
   - 隐私协议功能需要原生支持
   - 标准基座无法正常显示隐私协议

### 📱 测试要求
1. **真机测试**
   - 隐私协议功能只在真机上有效
   - 模拟器无法正常显示

2. **清除应用数据**
   - 测试前必须清除应用的本地数据
   - Android: 在设置中清除应用数据
   - iOS: 卸载并重新安装应用

### 🔗 协议链接
1. **确保协议链接可访问**
   - 协议链接: `https://zskj.asia/protocol.html`
   - 链接内容应包含完整的服务协议和隐私政策

2. **版本更新**
   - 如果需要用户重新同意协议，可以修改 `version` 字段
   - 版本号变化会触发重新显示协议弹窗

## 常见问题

### Q: 隐私协议弹窗不显示
A: 检查以下几点：
1. 确认 `androidPrivacy.json` 文件在项目根目录
2. 确认 `manifest.json` 中的 `privacy: {"prompt": "template"}` 配置
3. 确认 `manifest.json` 中的 `checkPermissionDenied` 和 `useOriginalMsgbox` 配置
4. 使用 HBuilderX 3.2.15+ 版本打包
5. 使用自定义调试基座进行测试
6. 清除应用数据后重新测试

### Q: 协议链接无法打开
A: 检查：
1. 网络连接是否正常
2. 协议链接是否可以在浏览器中正常访问
3. `hrefLoader` 配置是否正确

### Q: 用户拒绝后应用没有退出
A: 检查：
1. `backToExit` 配置
2. `disagreeMode` 中的相关设置
3. 二次确认弹窗的配置

## 配置检查清单

### ✅ 必须完成的配置
- [ ] `androidPrivacy.json` 文件存在于项目根目录
- [ ] `androidPrivacy.json` 中 `"prompt": "template"`
- [ ] `manifest.json` 中 `"privacy": {"prompt": "template"}`
- [ ] `manifest.json` 中 `"checkPermissionDenied": true`
- [ ] `manifest.json` 中 `"useOriginalMsgbox": true`
- [ ] 协议链接 `https://zskj.asia/protocol.html` 可正常访问

### ✅ 打包和测试要求
- [ ] 使用 HBuilderX 3.2.15+ 版本
- [ ] 制作自定义调试基座
- [ ] 在真机上测试
- [ ] 清除应用数据后重新测试

## 相关文件

- `androidPrivacy.json`: 隐私协议配置文件
- `manifest.json`: 应用配置文件
- `user/privacy.vue`: 隐私政策页面
- `user/configuser.vue`: 协议配置页面
- `pages/login.vue`: 登录页面（包含协议勾选）

## 版本更新功能

### 已复制的文件和功能

1. **utils/app-update.js**: 版本更新工具类
   - 支持版本检查、下载、安装
   - 支持强制更新和静默更新
   - 平台类型设置为 1（师傅端）

2. **components/app-update-check.vue**: 版本检查组件
   - 显示当前版本号
   - 提供手动检查更新功能

3. **pages/app-update.vue**: 版本更新页面
   - 完整的版本更新界面
   - 支持手动检查和更新

4. **App.vue 更新**
   - 添加启动时自动检查更新功能
   - 延迟3秒执行，避免影响启动速度

5. **API 接口**
   - 在 `api/modules/user.js` 中添加 `checkAppVersion` 接口

### 使用方法

1. **自动检查**: 应用启动后3秒自动检查更新
2. **手动检查**: 在设置页面添加版本检查组件
3. **版本更新页面**: 可以跳转到专门的更新页面进行操作

### 配置说明

- 师傅端平台类型为 1
- 用户端平台类型为 2
- 支持热更新（wgt包）和原生更新
- 支持强制更新和可选更新
