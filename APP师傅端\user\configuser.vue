<template>
	<view class="agreement-container">
		<view class="content-wrapper">
			<rich-text class="agreement-content" :nodes="info"></rich-text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info:''
			}
		},
		methods: {

		},
		onLoad(options) {
			console.log(options)
			if(options.type==="privacy"){
				this.$api.base.getConfig().then(res=>{
					console.log(res)
					this.info=res.content
					// console.log(this.info)
				})
			} else {
				// 默认显示服务协议
				this.$api.base.getConfig().then(res=>{
					console.log(res)
					this.info=res.loginProtocol
					// console.log(this.info)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.agreement-container {
	min-height: 100vh;
	background-color: #f8f9fa;
	padding: 20rpx;
}

.content-wrapper {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.agreement-content {
	line-height: 1.8;
	font-size: 28rpx;
	color: #333;
}
</style>
