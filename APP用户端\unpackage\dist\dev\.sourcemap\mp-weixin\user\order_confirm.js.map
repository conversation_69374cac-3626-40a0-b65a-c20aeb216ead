{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_confirm.vue?8192", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_confirm.vue?07f9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_confirm.vue?95e1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_confirm.vue?bcec", "uni-app:///user/order_confirm.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_confirm.vue?2540", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/order_confirm.vue?0a45"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "id", "type", "chooseArr", "chosenInputValues", "list", "list2", "list3", "serviceInfo", "newsubbit", "form", "carId", "serviceId", "tmplIds", "btArr", "focusedInputIndex", "keyboardHeight", "showprice", "yikoujiaprice", "windowHeight", "isKeyboardShow", "systemInfo", "scrollTimer", "isSubmitting", "computed", "footerStyle", "bottom", "methods", "getFormIndex", "handleInputFocus", "console", "clearTimeout", "handleInputBlur", "handleInput", "scrollToInput", "query", "inputRect", "pageScrollInfo", "inputAbsoluteTop", "visibleHeight", "safePosition", "targetScrollTop", "currentScrollTop", "uni", "scrollTop", "duration", "success", "fail", "imgUpload", "imgtype", "newFormData", "val", "getpzinfo", "res", "item", "name", "choose", "ids", "cartData", "originalItem", "option", "formItem", "problemDesc", "submit", "copy_form", "icon", "title", "open", "chooseOne", "getInfo", "onLoad", "onKeyboardHeightChange", "setTimeout", "onShow", "onHide", "onUnload", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4Fh3B;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAV;QACAW;QACAC;MACA;MAAAC,UACA,IACA,IACA,8CACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EAEAC;IACAC;MACA;QACAC;MACA;IACA;EACA;EAEAC;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MAAA;MACAC;MACA;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACA;MAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACA;MACA;IACA;IAEAC;MACAF;MACA;MACA;MACA;;MAEA;MACA;QACAC;QACA;MACA;IACA;IAEAE;MACAH;IACA;IAEAI;MAAA;MACA;;MAEA;MACAC;MACAA;MAEAA;QACA;UACA;UACA;UAEAL;YACAM;YACAC;YACAhB;UACA;;UAEA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACA;UACA;YACAL;UACA;;UAEA;UACA;;UAEA;UACA;;UAEA;UACA;UAEAc;YACAQ;YACAnB;YACAH;YACAuB;YACAC;YACAC;YACAC;UACA;;UAEA;UACA;YACAC;cACAC;cAAA;cACAC;cACAC;gBACAhB;cACA;cACAiB;gBACAjB;cACA;YACA;UACA;YACAA;UACA;QACA;UACAA;QACA;MACA;IACA;IAEAkB;MACA;QAAAC;MACA;MACAC,uDACAA;QACAC;MAAA,EACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAnD;gBACA;kBACA;kBACA6B;kBACAuB;oBACA;sBACA;oBACA;oBACAC;oBACAA;sBACA;wBACA1C;wBACA2C;wBACAC;sBACA;oBACA;kBACA;kBACA;oBAAA;kBAAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;kBACA;oBAAA;kBAAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;kBACA1B;kBACA;oBAAA;kBAAA;kBACA;oBACA;sBACA;sBACA;sBACA;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA,OAGA;kBACA2B;gBACA;kBACA;kBACA3B;kBAEA;kBACA;kBACA4B;oBACA;oBACA;sBAAA;oBAAA;oBAEA;sBACA;sBACA;wBAAA;sBAAA,MACA;wBAAA;sBAAA,MACA;wBAAA;sBAAA;sBAEA;wBACA;wBACA;0BACAC;4BACA;8BACAC;8BACA;8BACA;gCAAA;8BAAA;gCACA;8BACA;4BACA;0BACA;0BACA;0BACA;4BACAC;0BACA;;0BACA;4BACAA;0BACA;4BACAA;0BACA;wBAEA;wBACA;wBAAA,KACA;0BACAA;0BACA;0BACA;4BACAC;4BACAX;0BACA;wBACA;wBACA;wBAAA,KACA;0BACAU;4BAAA;0BAAA;wBACA;sBACA;oBACA;kBACA;;kBACA/B;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAiC;MAAA;MACA;MACA;QACA;MACA;MAEA;;MAEA;MACA;QAAA;UAAA9D;QAAA;MAAA;;MAEA;MACA;QACA;UAAA;QAAA;QACA;UACA+D;QACA;MACA;;MAEA;QACA;UAAA;QAAA;QACA;UACA;UACA;YACAA;UACA;UACAA;QACA;MACA;MAEA;MACAA;QACA;UAAA;QAAA;QACA;UACArB;YACAsB;YACAC;YACArB;UACA;UACAsB;UACA;QACA;QACA;QACA;UACAb;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;QAAA;QACA;QACA;UAAA;UAAA;YACArD;YAAA;YACAkD;UACA;QAAA;;QAEA;QACA;UACAnD;UACAY;UACAD;QACA;;QAEAmB;QAEA;UACA;YACAa;UACA;YACAA;cACAsB;cACAC;cACArB;YACA;UACA;QACA;UACAf;UACAa;YACAsB;YACAC;YACArB;UACA;QACA;UACA;QACA;MACA;IACA;IACAuB;MAAA;MACA;MACA;QAAA;QACA;UACA;UACAd;QACA;QACA;QACA;UACAA;YACA;cACA;YACA;UACA;QACA;MACA;QAAA;QACA;QACA;UACAA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAe;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACA;oBACA;oBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;EAEAC;IAAA;IACAxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAa;MACAG;QACA;QACA;QACAhB;MACA;IACA;EACA;EAEA;EACAyC;IAAA;IACAzC;IACA;IACA;;IAEA;IACA;MACA0C;QACA;MACA;IACA;EACA;EAEA;EACAC;IACA;IACA;IACA;IACA;IACA;MACA1C;MACA;IACA;EACA;EAEA;EACA2C;IACA;MACA3C;MACA;IACA;EACA;EAEA;EACA4C;IACA;MACA5C;MACA;IACA;EACA;EAEA6C;AACA;AAAA,2B;;;;;;;;;;;;;ACrjBA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/order_confirm.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/order_confirm.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_confirm.vue?vue&type=template&id=10e95c19&scoped=true&\"\nvar renderjs\nimport script from \"./order_confirm.vue?vue&type=script&lang=js&\"\nexport * from \"./order_confirm.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_confirm.vue?vue&type=style&index=0&id=10e95c19&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"10e95c19\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/order_confirm.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_confirm.vue?vue&type=template&id=10e95c19&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list2, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getFormIndex(item.id)\n    var m1 = _vm.getFormIndex(item.id)\n    var m2 = _vm.getFormIndex(item.id)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n    }\n  })\n  var l1 = _vm.__map(_vm.list3, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m3 = _vm.getFormIndex(item.id)\n    var m4 = _vm.getFormIndex(item.id)\n    return {\n      $orig: $orig,\n      m3: m3,\n      m4: m4,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_confirm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_confirm.vue?vue&type=script&lang=js&\"", "```javascript\n<template>\n  <view class=\"page\">\n <!--   <view class=\"header\">\n      <image :src=\"serviceInfo.cover\" mode=\"scaleToFill\"></image>\n    </view> -->\n    <view class=\"content\">\n      <view class=\"card\">\n<!--        <view class=\"top\">\n          <view class=\"title\">{{serviceInfo.title}}</view>\n          <view class=\"price\" v-if=\"serviceInfo.servicePriceType !=1\">￥{{serviceInfo.price}}</view>\n        </view> -->\n        <view class=\"bottom\">\n          <view class=\"left\">已选：</view>\n          <view class=\"right\">\n            \n            <view class=\"\">\n              {{yikoujiaprice}}\n            </view>\n            <view class=\"tag\" v-for=\"(item,index) in chooseArr\" :key=\"index\">{{item.name}}</view>\n            <view class=\"tag\" v-for=\"(item,index) in chosenInputValues\" :key=\"'input-' + index\">\n              {{ item.problemDesc }}: {{ item.val }}\n            </view>\n          </view>\n        </view>\n      </view>\n      <view class=\"chol\" v-for=\"(item,index) in list\" :key=\"index\">\n        <view class=\"choose\">\n          <view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n          <view class=\"desc\">{{item.problemContent}}</view>\n          <view class=\"cho_box\">\n            <view class=\"box_item\" v-for=\"(newItem,newIndex) in item.options\" :key=\"newIndex\"\n              :style=\"newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':''\"\n              @click=\"chooseOne(index,newIndex,item.inputType)\">\n              {{newItem.name}}\n              <view class=\"ok\" :style=\"newItem.choose? '' : 'display:none;'\">\n                <uni-icons type=\"checkmarkempty\" size=\"8\" color=\"#fff\"></uni-icons>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view class=\"fg\"></view>\n      </view>\n      \n      <view class=\"chol\" v-for=\"(item, index) in list2\" :key=\"item.id\">\n        <view class=\"choose\">\n          <view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n          <view class=\"desc\">{{item.problemContent}}</view>\n          <view class=\"input-container\" :id=\"'input-container-' + index\">\n            <input  \n              type=\"text\" \n              v-model=\"form.data[getFormIndex(item.id)].val\" \n              :placeholder=\"'请输入' + item.problemDesc\"\n              @focus=\"handleInputFocus(index)\"\n              @blur=\"handleInputBlur\"\n              @input=\"handleInput\"\n              class=\"form-input\"\n              cursor-spacing=\"10\"\n              confirm-type=\"done\"\n              :adjust-position=\"false\"\n              :auto-height=\"false\"\n            />\n          </view>\n        </view>\n        <view class=\"fg\"></view>\n      </view>\n      \n      <view class=\"chol\" v-for=\"(item,index) in list3\" :key=\"index\">\n        <view class=\"choose\">\n          <view class=\"title\"><span v-if=\"item.isRequired == 1\">*</span>{{item.problemDesc}}</view>\n          <view class=\"desc up\">{{item.problemContent}}</view>\n          <upload @upload=\"imgUpload\" @del=\"imgUpload\"\n            :imagelist=\"form.data[getFormIndex(item.id)].val\"\n            :imgtype=\"getFormIndex(item.id)\" text=\"上传图片\" :imgsize=\"3\">\n          </upload>\n        </view>\n        <view class=\"fg\"></view>\n      </view>\n      <view style=\"height: 300rpx;\"></view> \n    </view>\n    \n    <view class=\"footer\" :style=\"footerStyle\">\n      <view class=\"righ\" \n            :class=\"{ 'submitting': isSubmitting }\" \n            @click=\"submit\">\n        {{ isSubmitting ? '提交中...' : '保存' }}\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      id: '',\n      type: '',\n      chooseArr: [], // 存储单选多选已选项\n      chosenInputValues: [], // 新增：存储输入框已填值\n      list: [], // 单选多选框\n      list2: [], // 输入框\n      list3: [], // 上传图片\n      serviceInfo: {},\n\t  newsubbit:[],\n      form: {\n        data: [],\n        carId: '',\n\t\tserviceId:'',\n      },  tmplIds: [\n        '',\n        '',\n        'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n      ],\n      btArr: [], // 必填项\n      focusedInputIndex: -1,\n      keyboardHeight: 0,\n      showprice:false,\n      yikoujiaprice:'',\n      windowHeight: 0,\n      isKeyboardShow: false,\n      systemInfo: {},\n      scrollTimer: null,\n      isSubmitting: false // 新增：提交状态标记\n    }\n  },\n  \n  computed: {\n    footerStyle() {\n      return {\n        bottom: this.isKeyboardShow ? this.keyboardHeight + 'px' : '0px'\n      }\n    }\n  },\n  \n  methods: {\n    getFormIndex(serviceId) {\n      return this.form.data.findIndex(e => e.serviceId == serviceId);\n    },\n    handleInputFocus(index) {\n      console.log('输入框获得焦点:', index);\n      this.focusedInputIndex = index;\n      this.isKeyboardShow = true;\n      \n      // 清除之前的定时器\n      if (this.scrollTimer) {\n        clearTimeout(this.scrollTimer);\n      }\n      \n      // 多次尝试滚动，确保定位准确\n      this.scrollToInput(index);\n      \n      this.scrollTimer = setTimeout(() => {\n        this.scrollToInput(index);\n      }, 200);\n      \n      this.scrollTimer = setTimeout(() => {\n        this.scrollToInput(index);\n      }, 400);\n      \n      this.scrollTimer = setTimeout(() => {\n        this.scrollToInput(index);\n      }, 600);\n    },\n    \n    handleInputBlur() {\n      console.log('输入框失去焦点');\n      this.focusedInputIndex = -1;\n      this.isKeyboardShow = false;\n      this.keyboardHeight = 0;\n      \n      // 清除定时器\n      if (this.scrollTimer) {\n        clearTimeout(this.scrollTimer);\n        this.scrollTimer = null;\n      }\n    },\n    \n    handleInput(e) {\n      console.log('输入内容:', e.detail.value);\n    },\n    \n    scrollToInput(index) {\n      const query = uni.createSelectorQuery().in(this);\n      \n      // 同时获取输入框和页面信息\n      query.select(`#input-container-${index}`).boundingClientRect();\n      query.selectViewport().scrollOffset();\n      \n      query.exec((res) => {\n        if (res && res[0] && res[1]) {\n          const inputRect = res[0];\n          const pageScrollInfo = res[1];\n          \n          console.log('输入框位置信息:', {\n            inputRect,\n            pageScrollInfo,\n            systemInfo: this.systemInfo\n          });\n          \n          // 输入框距离页面顶部的绝对位置\n          const inputAbsoluteTop = inputRect.top + pageScrollInfo.scrollTop;\n          \n          // 获取当前系统信息\n          const systemInfo = uni.getSystemInfoSync();\n          const windowHeight = systemInfo.windowHeight;\n          const statusBarHeight = systemInfo.statusBarHeight || 0;\n          \n          // 预估键盘高度（一般占屏幕高度的40-50%）\n          let keyboardHeight = this.keyboardHeight;\n          if (!keyboardHeight || keyboardHeight < 100) {\n            keyboardHeight = windowHeight * 0.45; // 预估键盘高度\n          }\n          \n          // 计算可视区域高度（窗口高度 - 键盘高度）\n          const visibleHeight = windowHeight - keyboardHeight;\n          \n          // 计算安全位置：让输入框显示在可视区域的上1/3处\n          const safePosition = visibleHeight * 0.3;\n          \n          // 计算目标滚动位置\n          const targetScrollTop = inputAbsoluteTop - safePosition - statusBarHeight;\n          \n          console.log('滚动计算详情:', {\n            inputAbsoluteTop,\n            windowHeight,\n            keyboardHeight,\n            visibleHeight,\n            safePosition,\n            targetScrollTop,\n            currentScrollTop: pageScrollInfo.scrollTop\n          });\n          \n          // 只有当需要滚动的距离超过50px时才执行滚动\n          if (targetScrollTop > 0 && Math.abs(targetScrollTop - pageScrollInfo.scrollTop) > 50) {\n            uni.pageScrollTo({\n              scrollTop: Math.max(0, targetScrollTop), // 确保不会滚动到负数位置\n              duration: 300,\n              success: () => {\n                console.log('页面滚动成功到:', targetScrollTop);\n              },\n              fail: (err) => {\n                console.error('页面滚动失败:', err);\n              }\n            });\n          } else {\n            console.log('无需滚动或滚动距离太小');\n          }\n        } else {\n          console.error('获取元素位置信息失败:', res);\n        }\n      });\n    },\n    \n    imgUpload(e) {\n      let { imagelist, imgtype } = e;\n      let newFormData = [...this.form.data];\n      newFormData[imgtype] = {\n        ...newFormData[imgtype],\n        val: [...imagelist]\n      };\n      this.$set(this.form, 'data', newFormData);\n    },\n    \n    async getpzinfo() {\n      await this.$api.service.getcartsettingsInfo({\n        id: this.serviceId\n      }).then(ress => {\n        let res=ress.data\n        console.log(res)\n        res.forEach(item => {\n          if (item.isRequired == 1) {\n            this.btArr.push(item.id)\n          }\n          item.options = JSON.parse(item.options)\n          item.options = item.options.map(e => {\n            return {\n              serviceId: item.id,\n              name: e,\n              choose: false\n            }\n          })\n        })\n        this.list = res.filter(item => item.inputType == 3 || item.inputType == 4)\n        this.list.forEach((newItem, newIndex) => {\n          this.form.data.push({\n            \"serviceId\": newItem.id,\n            \"settingId\": this.id,\n            \"val\": []\n          })\n        })\n        this.list2 = res.filter(item => item.inputType == 1)\n        this.list2.forEach((newItem, newindex) => {\n          this.form.data.push({\n            \"serviceId\": newItem.id,\n            \"settingId\": this.id,\n            \"val\": ''\n          })\n        })\n        console.log(this.list2)\n        this.list3 = res.filter(item => item.inputType == 2)\n        this.list3.forEach((newItem, newindex) => {\n          this.form.data.push({\n            \"serviceId\": newItem.id,\n            \"settingId\": this.id,\n            \"val\": []\n          })\n        })\n      })\n      \n      // Fetch and render existing cart info\n      await this.$api.service.getcartinfo({\n        ids: this.id\n      }).then(ress => {\n        let cartData = ress.data[0].list;\n        console.log(\"Cart Info Data:\", cartData);\n\t\t\n        this.chosenInputValues = []; // Clear previous input values\n\t\t\tthis.newsubbit=cartData\n        cartData.forEach(cartItem => {\n          // Find the corresponding item in the form.data array\n          const formItemIndex = this.form.data.findIndex(f => f.serviceId === cartItem.settingId);\n\n          if (formItemIndex !== -1) {\n            const formItem = this.form.data[formItemIndex];\n            const originalItem = this.list.find(l => l.id === cartItem.settingId) ||\n                                 this.list2.find(l => l.id === cartItem.settingId) ||\n                                 this.list3.find(l => l.id === cartItem.settingId);\n\n            if (originalItem) {\n              // Handle single/multiple choice (inputType 3 or 4)\n              if (originalItem.inputType === 3 || originalItem.inputType === 4) {\n                originalItem.options.forEach(option => {\n                  if (option.name === cartItem.val) {\n                    option.choose = true;\n                    // Add to chooseArr if not already present\n                    if (!this.chooseArr.some(chosen => chosen.serviceId === option.serviceId && chosen.name === option.name)) {\n                      this.chooseArr.push(option);\n                    }\n                  }\n                });\n                // Also update form.data for these types\n                if (formItem.val && !Array.isArray(formItem.val)) {\n                    formItem.val = [formItem.val]; // Ensure it's an array for push\n                }\n                if (formItem.val && !formItem.val.includes(cartItem.val)) {\n                    formItem.val.push(cartItem.val);\n                } else if (!formItem.val) {\n                    formItem.val = [cartItem.val];\n                }\n\n              } \n              // Handle input fields (inputType 1)\n              else if (originalItem.inputType === 1) {\n                formItem.val = cartItem.val;\n                // Add to chosenInputValues for display in \"已选\"\n                this.chosenInputValues.push({\n                  problemDesc: originalItem.problemDesc,\n                  val: cartItem.val\n                });\n              } \n              // Handle image upload (inputType 2)\n              else if (originalItem.inputType === 2) {\n                formItem.val = cartItem.val.split(',').filter(url => url); // Split comma-separated URLs into an array\n              }\n            }\n          }\n        });\n        console.log(\"Updated chooseArr:\", this.chooseArr);\n        console.log(\"Updated form.data:\", this.form.data);\n      })\n    },\n    \n  submit() {\n    // 防止重复提交\n    if (this.isSubmitting) {\n      return;\n    }\n  \n    this.isSubmitting = true; // 设置提交状态\n  \n    let copy_form = JSON.parse(JSON.stringify(this.form));\n    let copynew = JSON.parse(JSON.stringify(this.newsubbit)).map(item => ({ id: item.id }));\n  \n    // Clear previous selections in copy_form.data for options\n    this.list.forEach(item => {\n      const formIndex = copy_form.data.findIndex(e => e.serviceId == item.id);\n      if (formIndex !== -1) {\n        copy_form.data[formIndex].val = []; // Reset array for options\n      }\n    });\n  \n    this.chooseArr.forEach(item => {\n      const formIndex = copy_form.data.findIndex(e => e.serviceId == item.serviceId);\n      if (formIndex !== -1) {\n        // Ensure val is an array before pushing\n        if (!Array.isArray(copy_form.data[formIndex].val)) {\n          copy_form.data[formIndex].val = [];\n        }\n        copy_form.data[formIndex].val.push(item.name);\n      }\n    });\n  \n    let open = true;\n    copy_form.data.forEach(item => {\n      let index = this.btArr.findIndex(e => e == item.serviceId);\n      if (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {\n        uni.showToast({\n          icon: 'none',\n          title: '请填写完整后提交',\n          duration: 1500\n        });\n        open = false;\n        return;\n      }\n      // Fill empty val with \"无\"\n      if (item.val === '' || (Array.isArray(item.val) && item.val.length === 0)) {\n        item.val = \"无\";\n      }\n    });\n  \n    if (!open) {\n      this.isSubmitting = false; // 验证失败时重置状态\n      return;\n    }\n  \n    if (open) {\n      // Map copy_form.data to include only id (from copynew) and val\n      const data = copy_form.data.map((item, index) => ({\n        id: copynew[index]?.id || item.serviceId, // Use copynew id if available, else fallback\n        val: Array.isArray(item.val) ? item.val.join(',') : item.val\n      }));\n  \n      // Prepare API payload with serviceId and settingId at the top level\n      const payload = {\n        data,\n        serviceId: this.form.serviceId,\n       carId: copy_form.data[0]?.serviceId || this.form.serviceId // Use first item's serviceId or fallback\n      };\n  \n      console.log('payload:', payload);\n  \n      this.$api.service.postorderinfo(payload).then(res => {\n        if (res.code === \"200\") {\n          uni.navigateBack(1);\n        } else {\n          uni.showToast({\n            icon: 'error',\n            title: '请重新尝试',\n            duration: 1000\n          });\n        }\n      }).catch(err => {\n        console.error('提交失败:', err);\n        uni.showToast({\n          icon: 'error',\n          title: '网络错误，请重试',\n          duration: 1000\n        });\n      }).finally(() => {\n        this.isSubmitting = false;\n      });\n    }\n  },\n    chooseOne(i, j, inputType) {\n      this.list[i].options[j].choose = !this.list[i].options[j].choose\n      if (inputType == 3) { // 单选\n        this.list[i].options.forEach((item, index) => {\n          if (index == j) return\n          item.choose = false\n        })\n        this.chooseArr = []\n        this.list.forEach(item => {\n          item.options.forEach(tem => {\n            if (tem.choose) {\n              this.chooseArr.push(tem)\n            }\n          })\n        })\n      } else if (inputType == 4) { // 多选\n        this.chooseArr = []\n        this.list.forEach(item => {\n          item.options.forEach(tem => {\n            if (tem.choose) {\n              this.chooseArr.push(tem)\n            }\n          })\n        })\n      }\n    },\n    \n    async getInfo() {\n      await this.$api.service.getserviceInfo(this.id).then(res => {\n        if(res.data.price !==0){\n          this.yikoujiaprice=res.data.price\n          this.showprice=true\n        }\n        this.showprice=false\n        this.serviceInfo = res.data\n      })\n    }\n  },\n  \n  onLoad(options) {\n    console.log(options)\n    this.id = options.id\n    this.serviceId = options.serviceId\n    this.type = options.type\n    this.form.serviceId = options.serviceId\n    this.form.carId = options.id\n    this.getInfo()\n    this.getpzinfo()\n    \n    // 获取系统信息\n    uni.getSystemInfo({\n      success: (res) => {\n        this.systemInfo = res;\n        this.windowHeight = res.windowHeight;\n        console.log('获取到系统信息:', res);\n      }\n    });\n  },\n  \n  // 监听键盘高度变化\n  onKeyboardHeightChange(res) {\n    console.log('键盘高度变化事件:', res);\n    this.keyboardHeight = res.height;\n    this.isKeyboardShow = res.height > 0;\n    \n    // 键盘高度变化时，如果有聚焦的输入框，重新调整位置\n    if (this.focusedInputIndex >= 0) {\n      setTimeout(() => {\n        this.scrollToInput(this.focusedInputIndex);\n      }, 50);\n    }\n  },\n  \n  // 页面显示时重置状态\n  onShow() {\n    this.focusedInputIndex = -1;\n    this.isKeyboardShow = false;\n    this.keyboardHeight = 0;\n    this.isSubmitting = false; // 重置提交状态\n    if (this.scrollTimer) {\n      clearTimeout(this.scrollTimer);\n      this.scrollTimer = null;\n    }\n  },\n  \n  // 页面隐藏时清理\n  onHide() {\n    if (this.scrollTimer) {\n      clearTimeout(this.scrollTimer);\n      this.scrollTimer = null;\n    }\n  },\n  \n  // 页面卸载时清理\n  onUnload() {\n    if (this.scrollTimer) {\n      clearTimeout(this.scrollTimer);\n      this.scrollTimer = null;\n    }\n  },\n  \n  watch: {}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n  min-height: 100vh;\n  position: relative;\n  padding-bottom: 200rpx;\n}\n\n.header {\n  width: 750rpx;\n  height: 376rpx;\n  position: absolute;\n  top: -300rpx;\n  left: 0;\n  z-index: -999;\n}\n\n.header image {\n  width: 100%;\n  height: 100%;\n}\n\n.content {\n  // margin-top: 280rpx;\n  margin-top: 10rpx;\n}\n\n.card {\n  margin-left: 32rpx;\n  width: 686rpx;\n  background: #FFFFFF;\n  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);\n  border-radius: 16rpx;\n  padding: 40rpx;\n}\n\n.card .top {\n  padding-bottom: 40rpx;\n  border-bottom: 2rpx solid #F2F3F6;\n}\n\n.card .top .title {\n  font-size: 36rpx;\n  font-weight: 500;\n  color: #171717;\n  letter-spacing: 2rpx;\n}\n\n.card .top .price {\n  margin-top: 12rpx;\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #E72427;\n}\n\n.card .bottom {\n  padding-top: 24rpx;\n  display: flex;\n}\n\n.card .bottom .left {\n  font-size: 24rpx;\n  font-weight: 400;\n  color: #999999;\n  padding-top: 10rpx;\n}\n\n.card .bottom .right {\n  flex: 1;\n  margin-left: 20rpx;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.card .bottom .right .tag {\n  width: fit-content;\n  height: 44rpx;\n  padding: 0 12rpx;\n  background: #DCEAFF;\n  border-radius: 4rpx;\n  font-size: 16rpx;\n  font-weight: 400;\n  color: #2E80FE;\n  line-height: 44rpx;\n  text-align: center;\n  margin: 10rpx;\n}\n\n.chol .choose {\n  padding: 40rpx 32rpx;\n}\n\n.chol .choose .title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333333;\n}\n\n.chol .choose .title span {\n  color: #E72427;\n}\n\n.chol .choose .input-container {\n  margin-top: 40rpx;\n  position: relative;\n  width: 100%;\n  min-height: 88rpx;\n}\n\n.chol .choose .form-input {\n  box-sizing: border-box;\n  width: 100%;\n  height: 88rpx;\n  background: #F7F7F7;\n  border-radius: 12rpx;\n  padding: 0 30rpx;\n  font-size: 28rpx;\n  line-height: 88rpx;\n  border: 2rpx solid transparent;\n  transition: all 0.2s ease;\n  position: relative;\n  z-index: 1;\n}\n\n.chol .choose .form-input:focus {\n  background: #fff;\n  border-color: #2E80FE;\n  box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);\n  outline: none;\n}\n\n.chol .choose .desc {\n  margin-top: 20rpx;\n  font-size: 24rpx;\n  font-weight: 400;\n  color: #ADADAD;\n}\n\n.chol .choose .up {\n  margin-bottom: 40rpx;\n}\n\n.chol .choose .cho_box {\n  margin-top: 20rpx;\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.chol .choose .cho_box .box_item {\n  width: fit-content;\n  padding: 0 20rpx;\n  height: 60rpx;\n  background: #FFFFFF;\n  border-radius: 4rpx;\n  border: 2rpx solid #D8D8D8;\n  font-size: 24rpx;\n  font-weight: 400;\n  color: #ADADAD;\n  line-height: 60rpx;\n  margin-right: 20rpx;\n  margin-bottom: 20rpx;\n  position: relative;\n}\n\n.chol .choose .cho_box .box_item .ok {\n  width: 20rpx;\n  height: 20rpx;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  background-color: #2E80FE;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.chol .fg {\n  width: 750rpx;\n  height: 20rpx;\n  background: #F3F4F5;\n}\n\n.footer {\n  padding: 38rpx 32rpx;\n  width: 750rpx;\n  background: #ffffff;\n  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 999;\n  transition: bottom 0.25s ease;\n\n  .righ {\n    width: 690rpx;\n    height: 88rpx;\n    background: #2e80fe;\n    border-radius: 44rpx;\n    font-size: 28rpx;\n    font-weight: 400;\n    color: #ffffff;\n    line-height: 88rpx;\n    text-align: center;\n    transition: all 0.2s ease;\n    \n    &.submitting {\n      background: #8bb8ff;\n      opacity: 0.7;\n      pointer-events: none;\n    }\n  }\n}\n\n/* iOS安全区域适配 */\n@supports (bottom: env(safe-area-inset-bottom)) {\n  .footer {\n    padding-bottom: calc(38rpx + env(safe-area-inset-bottom));\n  }\n}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_confirm.vue?vue&type=style&index=0&id=10e95c19&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_confirm.vue?vue&type=style&index=0&id=10e95c19&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754710509065\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}