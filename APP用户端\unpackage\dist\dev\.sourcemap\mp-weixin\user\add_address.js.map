{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?3578", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?dab5", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?833e", "uni-app:///user/add_address.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?d9f3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?5107"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "flag", "loading", "showCity", "menpai", "form", "userName", "mobile", "address", "addressInfo", "houseNumber", "city", "cityIds", "lng", "lat", "sex", "status", "provinceId", "cityId", "areaId", "columnsCity", "onLoad", "methods", "selectGender", "console", "parseCityInfo", "area", "province", "getNowPosition", "uni", "type", "isHighAccuracy", "accuracy", "success", "url", "resolve", "fail", "goMap", "scope", "setTimeout", "that", "title", "icon", "duration", "getLocationFallback", "res", "method", "geoRes", "cityInfo", "confirmCity", "getCity", "<PERSON><PERSON><PERSON><PERSON>", "columnIndex", "e", "index", "picker", "<PERSON><PERSON><PERSON><PERSON>", "findduration", "phoneReg", "key", "mergeName", "userId", "subForm", "delta", "checkAppVersion"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiD92B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAEAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,cACA;MAAA;MACA;MAAA;MACA;MAAA;IAEA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;IACA;IACA;IACAC;MACA;QACA;UAAAb;UAAAD;QAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,uBACA;MAEA;QAAA;QACA;QACA;UACA;YAAAA;YAAAe;UAEA;YACA;YACAC;YACAhB;YACAe;UACA;YACA;YACAC;YACAhB;YACAe;UACA;YACA;YACAC;YACAhB;YACAe;UACA;YACA;YACAC;YACAhB;YACAe;UACA;;UAEA;UACAC;UACAhB;UACAe;UAEA;YACAd;YACAD;UACA;QACA;MACA;;MAEA;MACAa;MACA;QAAAZ;QAAAD;MAAA;IACA;IAEAiB;MAAA;MACA;QACAC;UACAC;UACAC;UACAC;UACAC;YACAJ;YACAA;YACAA;cACAK;cACAD;gBACAT;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAW;cACA;cACAC;gBACAZ;gBACAW;cACA;YACA;UACA;UACAC;YACAZ;YACAW;UACA;QACA;MACA;IACA;IAKAE;MACA;MAEAR;QACAS;QACAL;UACA;UACAM;YACAV;cACAI;gBACAT;gBACA;kBACA;kBACA;kBACAgB,kCACAA,0CACA;kBACAA,+BACAA,uCACA;kBACAhB;kBACAA;kBAEAgB;kBACAA;kBACAA;kBACAA;kBAEAX;oBACAY;oBACAC;oBACAC;kBACA;gBACA;kBACAnB;kBACAK;oBACAY;oBACAC;oBACAC;kBACA;gBACA;cACA;cACAP;gBACAZ;gBACAK;kBACAY;kBACAC;kBACAC;gBACA;cACA;YACA;UACA;QACA;QACAP;UACAZ;UACAK;YACAY;YACAC;YACAC;UACA;QACA;MACA;IA+GA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAf;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAJAa;gBAAA;gBAAA,OAOAhB;kBACAK;kBACAY;gBACA;cAAA;gBAHAC;gBAKA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;oBACAC;oBACA;oBACA;kBACA;kBAEAnB;oBACAY;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnB;gBACAK;kBACAY;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAM;MAAA;MACA;MACA;MACA;QAAA;QACA;MACA;MACA;QAAA;QACA;MACA;QAAA;MAAA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QAAA;QACA;QACA;UACA;YAAA;YACA;YACA;cACA;gBACA;gBACA;cAEA;YACA;UACA;QACA;MACA;QACA1B;MACA;IACA;IACA2B;MAAA;MACA,IACAC,cAGAC,EAHAD;QACAE,QAEAD,EAFAC;QAAA,YAEAD,EADAE;QAAAA;MAEA;QAAA;QACA;UACA;YAAA;YACAA;YACA;YACA;cACA;gBACAA;gBACA;gBACA/B;cACA;YACA;UACA;QACA;MACA;QAAA;QACA;UACA;YACA+B;YACA;YACA/B;UACA;QACA;MACA;IACA;IACAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA3B;kBACAa;kBACAD;kBACAgB;gBACA;gBAAA;cAAA;gBAGAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA7B;kBACAa;kBACAD;kBACAE;gBACA;gBAAA;cAAA;gBAGAnB;gBAAA,gBACA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAmC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA9B;kBACAa;kBACAD;kBACAE;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAd;kBACAa;kBACAD;kBACAE;gBACA;gBAAA;cAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAiB;gBACA;cAAA;gBAFAf;gBAGArB;gBACA;kBACA;kBACA;kBACAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAK;kBACAa;kBACAD;kBACAE;gBACA;gBAAA;cAAA;gBAIA;gBACAkB;gBACArC;gBACAA;gBACA;gBACAsC;kBACAtD;kBACAC;kBACA;kBACAE;kBACA;kBACAC;kBAAA;kBACA;kBACAF;kBACA;kBACAI;kBACAD;kBACAN;kBACA;kBACAQ;kBACAC;kBACA;kBACA;;kBAEA;kBACAV;gBACA;gBACA;kBACA;oBACAuB;sBACAa;sBACAD;sBACAE;oBACA;oBACAJ;sBACAV;wBAAAkC;sBAAA;oBACA;kBACA;oBACAlC;sBACAa;sBACAD;sBACAE;oBACA;kBACA;gBACA;kBACAd;oBACAa;oBACAD;oBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqB,6CAeA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrmBA;AAAA;AAAA;AAAA;AAAimD,CAAgB,qjDAAG,EAAC,C;;;;;;;;;;;ACArnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/add_address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/add_address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add_address.vue?vue&type=template&id=99ba6ec0&scoped=true&\"\nvar renderjs\nimport script from \"./add_address.vue?vue&type=script&lang=js&\"\nexport * from \"./add_address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"99ba6ec0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/add_address.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=template&id=99ba6ec0&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-switch/u-switch\" */ \"uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=script&lang=js&\"", "\r\n<template>\r\n\t<view class=\"page\">\r\n\t\t<u-picker :show=\"showCity\" ref=\"uPicker\" :loading=\"loading\" :columns=\"columnsCity\" @change=\"changeHandler\"\r\n\t\t\tkeyName=\"title\" @cancel=\"showCity = false\" @confirm=\"confirmCity\" v-if=\"flag\"></u-picker>\r\n\t\t<view class=\"top\">个人信息隐私信息完全保密</view>\r\n\t\t<view class=\"main\">\r\n\t\t\t<view class=\"main_item \" @tap=\"goMap\">\r\n\t\t\t\t<view class=\"name\">服务地址</view>\r\n\t\t\t\t<view class=\"address\">\r\n\t\t\t\t\t<span>{{form.address}}</span>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image src=\"../static/images/position.png\" mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t<!-- 备用位置获取按钮，当主要方法失败时显示 -->\r\n\t\t\t\r\n\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"name\">门牌号</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.houseNumber\" placeholder=\"请输入详细地址，如7栋4单元18a\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"name\">联系人</view>\r\n\t\t\t\t<input type=\"text\" v-model=\"form.userName\" placeholder=\"请输入姓名\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"name\">性别</view>\r\n\t\t\t\t<view class=\"box\">\r\n\t\t\t\t\t<view class=\"box_item\"\r\n\t\t\t\t\t\t:style=\"form.sex == 1?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''\"\r\n\t\t\t\t\t\t@click=\"selectGender(1)\">先生</view>\r\n\t\t\t\t\t<view class=\"box_item\"\r\n\t\t\t\t\t\t:style=\"form.sex == 2?'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE':''\"\r\n\t\t\t\t\t\t@click=\"selectGender(2)\">女士</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item\">\r\n\t\t\t\t<view class=\"name\">手机号码</view>\r\n\t\t\t\t<input type=\"tel\" v-model=\"form.mobile\" placeholder=\"请输入手机号码\">\r\n\t\t\t</view>\r\n\t\t\t<view class=\"main_item last\">\r\n\t\t\t\t<view class=\"name\">设为默认地址</view>\r\n\t\t\t\t<u-switch v-model=\"form.status\" activeColor=\"#2E80FE\"></u-switch>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @click=\"SaveAddress\">保存</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tflag: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tshowCity: false,\r\n\t\t\t\tmenpai: '',\r\n\t\t\t\t\r\n\t\t\t\tform: {\r\n\t\t\t\t\tuserName: '',\r\n\t\t\t\t\tmobile: '',\r\n\t\t\t\t\taddress: '点击选择服务地址',\r\n\t\t\t\t\taddressInfo: '',\r\n\t\t\t\t\thouseNumber: '',\r\n\t\t\t\t\tcity: '',\r\n\t\t\t\t\tcityIds: '',\r\n\t\t\t\t\tlng: '',\r\n\t\t\t\t\tlat: '',\r\n\t\t\t\t\r\n\t\t\t\t\tsex: 1,\r\n\t\t\t\t\tstatus: false,\r\n\t\t\t\t\tprovinceId: 0,\r\n\t\t\t\t\tcityId: 0,\r\n\t\t\t\t\tareaId: 0\r\n\t\t\t\t},\r\n\t\t\t\tcolumnsCity: [\r\n\t\t\t\t\t[], // Province\r\n\t\t\t\t\t[], // City\r\n\t\t\t\t\t[]  // Area\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// this.getCity(0)\r\n\t\t\tthis.getNowPosition()\r\n\t\t\tthis.checkAppVersion()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 性别选择方法\r\n\t\t\tselectGender(gender) {\r\n\t\t\t\tthis.form.sex = gender;\r\n\t\t\t\tconsole.log('Selected gender:', gender);\r\n\t\t\t},\r\n\t\t\t// 解析城市信息的通用方法\r\n\t\t\tparseCityInfo(address) {\r\n\t\t\t\tif (!address || typeof address !== 'string') {\r\n\t\t\t\t\treturn { cityIds: '', city: '' };\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 处理各种地址格式的正则表达式\r\n\t\t\t\tconst patterns = [\r\n\t\t\t\t\t// 标准格式：省+市+区/县\r\n\t\t\t\t\t/^(.+?省)(.+?市)(.+?[县区]).*$/,\r\n\t\t\t\t\t// 自治区格式：自治区+市+区/县/旗\r\n\t\t\t\t\t/^(.+?自治区)(.+?市)(.+?[县区旗]).*$/,\r\n\t\t\t\t\t// 自治区+盟+市格式：自治区+盟+市\r\n\t\t\t\t\t/^(.+?自治区)(.+?盟)(.+?市).*$/,\r\n\t\t\t\t\t// 直辖市格式：市+区/县\r\n\t\t\t\t\t/^(北京|上海|天津|重庆)(市)?(.+?[县区]).*$/,\r\n\t\t\t\t\t// 特别行政区格式\r\n\t\t\t\t\t/^(香港|澳门)(.+?区)?(.*)$/\r\n\t\t\t\t];\r\n\r\n\t\t\t\tfor (let pattern of patterns) {\r\n\t\t\t\t\tconst match = address.match(pattern);\r\n\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\tlet province, city, area;\r\n\r\n\t\t\t\t\t\tif (pattern.source.includes('北京|上海|天津|重庆')) {\r\n\t\t\t\t\t\t\t// 直辖市处理\r\n\t\t\t\t\t\t\tprovince = match[1];\r\n\t\t\t\t\t\t\tcity = match[1] + '市';\r\n\t\t\t\t\t\t\tarea = match[3] || '';\r\n\t\t\t\t\t\t} else if (pattern.source.includes('香港|澳门')) {\r\n\t\t\t\t\t\t\t// 特别行政区处理\r\n\t\t\t\t\t\t\tprovince = match[1];\r\n\t\t\t\t\t\t\tcity = match[1];\r\n\t\t\t\t\t\t\tarea = match[2] || match[3] || '';\r\n\t\t\t\t\t\t} else if (pattern.source.includes('盟')) {\r\n\t\t\t\t\t\t\t// 自治区+盟+市格式处理\r\n\t\t\t\t\t\t\tprovince = match[1];\r\n\t\t\t\t\t\t\tcity = match[2];  // 盟作为市级\r\n\t\t\t\t\t\t\tarea = match[3];  // 市作为区级\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 标准省市区处理\r\n\t\t\t\t\t\t\tprovince = match[1];\r\n\t\t\t\t\t\t\tcity = match[2];\r\n\t\t\t\t\t\t\tarea = match[3];\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 清理空白字符\r\n\t\t\t\t\t\tprovince = province.trim();\r\n\t\t\t\t\t\tcity = city.trim();\r\n\t\t\t\t\t\tarea = area.trim();\r\n\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tcityIds: `${province},${city},${area}`,\r\n\t\t\t\t\t\t\tcity: `${province}-${city}-${area}`\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 如果都不匹配，返回空值\r\n\t\t\t\tconsole.warn('无法解析地址格式:', address);\r\n\t\t\t\treturn { cityIds: '', city: '' };\r\n\t\t\t},\r\n\r\n\t\t\tgetNowPosition() {\r\n\t\t\t\treturn new Promise((resolve) => {\r\n\t\t\t\t\tuni.getLocation({\r\n\t\t\t\t\t\ttype: \"gcj02\",\r\n\t\t\t\t\t\tisHighAccuracy: true,\r\n\t\t\t\t\t\taccuracy: \"best\",\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tuni.setStorageSync(\"lat\", res.latitude);\r\n\t\t\t\t\t\t\tuni.setStorageSync(\"lng\", res.longitude);\r\n\t\t\t\t\t\t\tuni.request({\r\n\t\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,\r\n\t\t\t\t\t\t\t\tsuccess: (res1) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log(res1)\r\n\t\t\t\t\t\t\t\t\tthis.form.address = res1.data.regeocode.formatted_address\r\n\t\t\t\t\t\t\t\t\t// 使用新的城市信息解析方法\r\n\t\t\t\t\t\t\t\t\tconst cityInfo = this.parseCityInfo(res1.data.regeocode.formatted_address);\r\n\t\t\t\t\t\t\t\t\tthis.form.cityIds = cityInfo.cityIds;\r\n\t\t\t\t\t\t\t\t\tthis.form.city = cityInfo.city;\r\n\t\t\t\t\t\t\t\t\t// Store coordinates\r\n\t\t\t\t\t\t\t\t\tthis.form.lng = res.longitude;\r\n\t\t\t\t\t\t\t\t\tthis.form.lat = res.latitude;\r\n\t\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error(\"逆地理编码失败:\", err);\r\n\t\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error(\"获取定位失败:\", err);\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tgoMap() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tuni.authorize({\r\n\t\t\t\t\tscope: 'scope.userLocation',\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t// 添加延时和错误处理\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('选择位置成功:', res);\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\t// Split address into province, city, county format, discarding anything after county\r\n\t\t\t\t\t\t\t\t\t\t// Use 'that' to correctly reference 'this.form.cityIds'\r\n\t\t\t\t\t\t\t\t\t\tthat.form.cityIds = res.address\r\n\t\t\t\t\t\t\t\t\t\t  ? that.parseCityInfo(res.address).cityIds\r\n\t\t\t\t\t\t\t\t\t\t  : '';\r\n\t\t\t\t\t\t\t\t\t\tthat.form.city = res.address\r\n\t\t\t\t\t\t\t\t\t\t  ? that.parseCityInfo(res.address).city\r\n\t\t\t\t\t\t\t\t\t\t  : '';\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('处理后的cityIds:', that.form.cityIds);\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('处理后的city:', that.form.city);\r\n\r\n\t\t\t\t\t\t\t\t\t\tthat.form.address = res.name || '未知位置'\r\n\t\t\t\t\t\t\t\t\t\tthat.form.addressInfo = res.address || ''\r\n\t\t\t\t\t\t\t\t\t\tthat.form.lng = res.longitude || ''\r\n\t\t\t\t\t\t\t\t\t\tthat.form.lat = res.latitude || ''\r\n\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '位置选择成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error('处理位置信息时出错:', error);\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '位置信息处理失败',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\tconsole.error('选择位置失败:', err);\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '选择位置失败，请重试',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 300); // 延时300ms避免框架内部状态问题\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\tconsole.error('位置授权失败:', err)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请授权位置信息',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP\r\n\t\t\t\t// APP端需要更长的延时来避免内部状态问题\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tconsole.log('APP选择位置成功:', res)\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t// 严格检查res对象的完整性，包括pageIndex属性\r\n\t\t\t\t\t\t\t\t\tif (!res || typeof res !== 'object') {\r\n\t\t\t\t\t\t\t\t\t\tthrow new Error('返回的位置信息格式异常');\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t// 检查是否存在pageIndex为null的情况（这是导致错误的根本原因）\r\n\t\t\t\t\t\t\t\t\tif (res.hasOwnProperty('pageIndex') && res.pageIndex === null) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.warn('检测到pageIndex为null，但位置信息正常，继续处理');\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t// APP端也需要处理cityIds和city字段，与小程序端保持一致\r\n\t\t\t\t\t\t\t\t\tif (res.address && typeof res.address === 'string') {\r\n\t\t\t\t\t\t\t\t\t\tconst cityInfo = that.parseCityInfo(res.address);\r\n\t\t\t\t\t\t\t\t\t\tthat.form.cityIds = cityInfo.cityIds;\r\n\t\t\t\t\t\t\t\t\t\tthat.form.city = cityInfo.city;\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tthat.form.cityIds = '';\r\n\t\t\t\t\t\t\t\t\t\tthat.form.city = '';\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t// 安全地设置各个字段\r\n\t\t\t\t\t\t\t\t\tthat.form.address = (res.name && typeof res.name === 'string') ? res.name : '选择的位置'\r\n\t\t\t\t\t\t\t\t\tthat.form.addressInfo = (res.address && typeof res.address === 'string') ? res.address : ''\r\n\t\t\t\t\t\t\t\t\tthat.form.lng = (res.longitude && typeof res.longitude === 'number') ? res.longitude.toString() : ''\r\n\t\t\t\t\t\t\t\t\tthat.form.lat = (res.latitude && typeof res.latitude === 'number') ? res.latitude.toString() : ''\r\n\r\n\t\t\t\t\t\t\t\t\tconsole.log('APP端处理后的cityIds:', that.form.cityIds);\r\n\t\t\t\t\t\t\t\t\tconsole.log('APP端处理后的city:', that.form.city);\r\n\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '位置选择成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\tconsole.error('APP处理位置信息时出错:', error);\r\n\t\t\t\t\t\t\t\t\t// 即使处理出错，也要设置基本信息\r\n\t\t\t\t\t\t\t\t\tthat.form.address = '位置信息'\r\n\t\t\t\t\t\t\t\t\tthat.form.addressInfo = ''\r\n\t\t\t\t\t\t\t\t\tthat.form.lng = ''\r\n\t\t\t\t\t\t\t\t\tthat.form.lat = ''\r\n\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '位置信息处理失败，请重新选择',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2500\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\tconsole.error('APP选择位置失败:', err);\r\n\r\n\t\t\t\t\t\t\t\t// 检查是否是pageIndex相关的错误\r\n\t\t\t\t\t\t\t\tif (err && err.errMsg && (err.errMsg.includes('pageIndex') || err.errMsg.includes('null'))) {\r\n\t\t\t\t\t\t\t\t\tconsole.error('检测到pageIndex为null的错误，这是HBuilderX版本兼容性问题');\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '地图组件异常，请使用\"获取当前位置\"功能',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\tduration: 3500\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// 区分不同的错误类型\r\n\t\t\t\t\t\t\t\tif (err && err.errMsg) {\r\n\t\t\t\t\t\t\t\t\tif (err.errMsg.includes('cancel')) {\r\n\t\t\t\t\t\t\t\t\t\t// 用户取消选择，不显示错误提示\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('用户取消了位置选择');\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t} else if (err.errMsg.includes('auth')) {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '请授权位置权限',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\tduration: 2500\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '位置选择失败，建议使用\"获取当前位置\"',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '地图功能异常，请尝试其他方式',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\tduration: 2500\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (globalError) {\r\n\t\t\t\t\t\tconsole.error('APP端chooseLocation调用失败:', globalError);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '地图功能暂时不可用',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 500); // APP端使用更长的延时\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t// 备用的位置获取方法\r\n\t\t\tasync getLocationFallback() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await uni.getLocation({\r\n\t\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\t\tisHighAccuracy: true,\r\n\t\t\t\t\t\taccuracy: 'best'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 使用高德地图API进行逆地理编码\r\n\t\t\t\t\tconst geoRes = await uni.request({\r\n\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=${res.longitude},${res.latitude}`,\r\n\t\t\t\t\t\tmethod: 'GET'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tif (geoRes.data && geoRes.data.regeocode) {\r\n\t\t\t\t\t\tthis.form.address = geoRes.data.regeocode.formatted_address || '当前位置';\r\n\t\t\t\t\t\tthis.form.addressInfo = geoRes.data.regeocode.formatted_address || '';\r\n\t\t\t\t\t\tthis.form.lng = res.longitude;\r\n\t\t\t\t\t\tthis.form.lat = res.latitude;\r\n\r\n\t\t\t\t\t\t// 处理城市信息\r\n\t\t\t\t\t\tif (geoRes.data.regeocode.formatted_address) {\r\n\t\t\t\t\t\t\tconst cityInfo = this.parseCityInfo(geoRes.data.regeocode.formatted_address);\r\n\t\t\t\t\t\t\tthis.form.cityIds = cityInfo.cityIds;\r\n\t\t\t\t\t\t\tthis.form.city = cityInfo.city;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '已获取当前位置',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('备用位置获取失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '位置获取失败，请手动输入',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tconfirmCity(Array) {\r\n\t\t\t\t// Map selected values to titles and IDs\r\n\t\t\t\tconst selectedValues = Array.value\r\n\t\t\t\tconst titles = selectedValues.map((item, index) => {\r\n\t\t\t\t\treturn item?.title || this.columnsCity[index][0]?.title || ''\r\n\t\t\t\t})\r\n\t\t\t\tconst ids = selectedValues.map((item, index) => {\r\n\t\t\t\t\treturn item?.id || this.columnsCity[index][0]?.id || 0\r\n\t\t\t\t}).filter(id => id !== null && id !== undefined)\r\n\r\n\t\t\t\tthis.form.city = titles.join('-')\r\n\t\t\t\t// Set cityIds as nested array [[provinceId, cityId, areaId]]\r\n\t\t\t\tthis.form.cityIds = ids.length >= 3 ? [[ids[0], ids[1], ids[2]]] : [[0, 0, 0]]\r\n\t\t\t\t// Set individual IDs\r\n\t\t\t\tthis.form.provinceId = ids[0] || 0\r\n\t\t\t\tthis.form.cityId = ids[1] || 0\r\n\t\t\t\tthis.form.areaId = ids[2] || 0\r\n\t\t\t\tthis.showCity = false\r\n\t\t\t},\r\n\t\t\tgetCity(e) {\r\n\t\t\t\tthis.$api.service.getCity(e).then(res => {\r\n\t\t\t\t\tthis.columnsCity[0] = res\r\n\t\t\t\t\tif (res[0]?.id) {\r\n\t\t\t\t\t\tthis.$api.service.getCity(res[0].id).then(res1 => {\r\n\t\t\t\t\t\t\tthis.columnsCity[1] = res1\r\n\t\t\t\t\t\t\tif (res1[0]?.id) {\r\n\t\t\t\t\t\t\t\tthis.$api.service.getCity(res1[0].id).then(res2 => {\r\n\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = res2\r\n\t\t\t\t\t\t\t\t\tthis.flag = true\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error('Failed to fetch city data:', err)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchangeHandler(e) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcolumnIndex,\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\tpicker = this.$refs.uPicker\r\n\t\t\t\t} = e\r\n\t\t\t\tif (columnIndex === 0) {\r\n\t\t\t\t\tif (this.columnsCity[0][index]?.id) {\r\n\t\t\t\t\t\tthis.$api.service.getCity(this.columnsCity[0][index].id).then(res => {\r\n\t\t\t\t\t\t\tpicker.setColumnValues(1, res)\r\n\t\t\t\t\t\t\tthis.columnsCity[1] = res\r\n\t\t\t\t\t\t\tif (res[0]?.id) {\r\n\t\t\t\t\t\t\t\tthis.$api.service.getCity(res[0].id).then(res1 => {\r\n\t\t\t\t\t\t\t\t\tpicker.setColumnValues(2, res1)\r\n\t\t\t\t\t\t\t\t\tthis.columnsCity[2] = res1\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(res1)\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (columnIndex === 1) {\r\n\t\t\t\t\tif (this.columnsCity[1][index]?.id) {\r\n\t\t\t\t\t\tthis.$api.service.getCity(this.columnsCity[1][index].id).then(res => {\r\n\t\t\t\t\t\t\tpicker.setColumnValues(2, res)\r\n\t\t\t\t\t\t\tthis.columnsCity[2] = res\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync SaveAddress() {\r\n\t\t\t\tif (this.form.address === '点击选择服务地址') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请填写完整提交',\r\n\t\t\t\t\t\tfindduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet phoneReg = /^1[3456789]\\d{9}$/\r\n\t\t\t\tif (!phoneReg.test(this.form.mobile)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请填写正确的手机号',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(this.form)\r\n\t\t\t\tfor (let key of ['userName', 'mobile', 'address', 'houseNumber']) {\r\n\t\t\t\t\tif (this.form[key] === '') {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: '请填写完整提交',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 检查城市信息是否有效\r\n\t\t\t\tif (!this.form.cityIds || this.form.cityIds === '') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请选择所在区域',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tif (this.form.cityIds) { // Only call if cityIds has a value from goMap\r\n\t\t\t\t  try {\r\n\t\t\t\t    const res = await this.$api.service.getZhuanhuan({\r\n\t\t\t\t      mergeName: this.form.cityIds\r\n\t\t\t\t    });\r\n\t\t\t\t    console.log(res);\r\n\t\t\t\t    if (res.data) {\r\n\t\t\t\t      // Construct the comma-separated string from the individual IDs\r\n\t\t\t\t      this.form.cityIds = `${res.data.provinceId},${res.data.cityId},${res.data.areaId}`;\r\n\t\t\t\tconsole.log(this.form.cityIds)\r\n\t\t\t\t    } else {\r\n\t\t\t\t      this.form.cityIds = ''; // Handle cases where res.data might be null or undefined\r\n\t\t\t\t    }\r\n\t\t\t\t  } catch (err) {\r\n\t\t\t\t    console.error(\"Error converting cityIds:\", err);\r\n\t\t\t\t    uni.showToast({\r\n\t\t\t\t      icon: 'none',\r\n\t\t\t\t      title: '城市信息转换失败',\r\n\t\t\t\t      duration: 1500,\r\n\t\t\t\t    });\r\n\t\t\t\t    return; // Stop execution if conversion fails\r\n\t\t\t\t  }\r\n\t\t\t\t}\r\n\t\t\t\t// Prepare form data for API\r\n\t\t\t\tlet userId= uni.getStorageSync('userInfo')\r\n\t\t\t\tconsole.log(userId)\r\n\t\t\t\tconsole.log(this.form)\r\n\t\t\t\t// console.log(JSON.parse(userId))\r\n\t\t\t\tlet subForm = {\r\n\t\t\t\t\taddress: this.form.address,\r\n\t\t\t\t\taddressInfo: this.form.addressInfo,\r\n\t\t\t\t\t// areaId: this.form.areaId,\r\n\t\t\t\t\tcity: this.form.city,\r\n\t\t\t\t\t// cityId: this.form.cityId,\r\n\t\t\t\t\tcityIds: this.form.cityIds, // [[provinceId, cityId, areaId]]\r\n\t\t\t\t\t// createTime: 0,\r\n\t\t\t\t\thouseNumber: this.form.houseNumber,\r\n\t\t\t\t\t// id: 0,\r\n\t\t\t\t\tlat: this.form.lat,\r\n\t\t\t\t\tlng: this.form.lng,\r\n\t\t\t\t\tmobile: this.form.mobile,\r\n\t\t\t\t\t// provinceId: this.form.provinceId,\r\n\t\t\t\t\tsex: this.form.sex,\r\n\t\t\t\t\tstatus: this.form.status ? 1 : 0,\r\n\t\t\t\t\t// top: 0,\r\n\t\t\t\t\t// uniacid: this.form.uniacid,\r\n\t\t\t\t\t\r\n\t\t\t\t\t// userId:userId.userId,\r\n\t\t\t\t\tuserName: this.form.userName\r\n\t\t\t\t}\r\n\t\t\t\tthis.$api.service.postAddAddress(subForm).then(res => {\r\n\t\t\t\t\tif(res.code==='200'){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: '提交成功',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack({ delta: 1 })\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: err.msg || '提交失败，重新尝试',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: err.msg || '提交失败',\r\n\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 检查APP版本和环境\r\n\t\t\tcheckAppVersion() {\r\n\t\t\t\t// #ifdef APP\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\r\n\t\t\t\t\tconsole.log('系统信息:', systemInfo);\r\n\r\n\t\t\t\t\t// 如果是开发环境，提示版本问题\r\n\t\t\t\t\tif (systemInfo.platform === 'android' || systemInfo.platform === 'ios') {\r\n\t\t\t\t\t\tconsole.warn('当前使用的HBuilderX版本可能存在chooseLocation的bug');\r\n\t\t\t\t\t\tconsole.warn('建议降级到HBuilderX 4.45或3.8.12版本');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取系统信息失败:', error);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.page {\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t.top {\r\n\t\t\twidth: 750rpx;\r\n\t\t\theight: 58rpx;\r\n\t\t\tbackground: #FFF7F1;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #FE921B;\r\n\t\t\tline-height: 58rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t.btn {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tmargin-top: 88rpx;\r\n\t\t\twidth: 690rpx;\r\n\t\t\theight: 98rpx;\r\n\t\t\tbackground: #2E80FE;\r\n\t\t\tborder-radius: 50rpx 50rpx 50rpx 50rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tline-height: 98rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t.main {\r\n\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t.main_item {\r\n\t\t\t\tpadding: 40rpx 0;\r\n\t\t\t\tborder-bottom: 2rpx solid #E9E9E9;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.name {\r\n\t\t\t\t\tmin-width: 112rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tmargin-right: 40rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.address {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #ADADAD;\r\n\r\n\t\t\t\t\t.details {\r\n\t\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #ADADAD;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 23rpx;\r\n\t\t\t\t\theight: 27rpx;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\ttop: 46rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinput {\r\n\t\t\t\t\twidth: 450rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.box_item {\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\twidth: 88rpx;\r\n\t\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\tborder: 2rpx solid #EDEDED;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #ADADAD;\r\n\t\t\t\t\t\tline-height: 46rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.last {\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.name {\r\n\t\t\t\t\twidth: 170rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.fallback_location {\r\n\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.fallback_text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #2E80FE;\r\n\t\t\t\ttext-decoration: underline;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754708168893\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}