{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/coachCashOut.vue?d836", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/coachCashOut.vue?15b3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/coachCashOut.vue?467d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/coachCashOut.vue?4bb5", "uni-app:///shifu/coachCashOut.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/coachCashOut.vue?2aea", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/coachCashOut.vue?6e9c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "money", "allmoney", "tempForm", "<PERSON><PERSON><PERSON>", "idCode", "id_card1", "id_card2", "isSubmitting", "showNameIdModal", "mchId", "debounceConfirmTx", "onLoad", "methods", "imgUploadTemp", "console", "imgtype", "saveNameIdInfo", "uni", "icon", "title", "p", "shi<PERSON><PERSON>", "userId", "payload", "id", "idCard", "path", "res", "debounce", "args", "timer", "fn", "confirmTx", "amount", "content", "confirmText", "cancelText", "success", "showCancel", "type", "appId", "package", "setTimeout", "url", "fail", "complete", "goAll", "change", "getMoney", "copyPhoneNumber"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoE/2B;EACAC;IACA;MACAC;MACAC;MACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;;EACAC;IACAC;MACAC;MACA;QAAAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,iBAMA,gBAJAb,sCACAC,gCACAC,oCACAC;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAW;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGAE;gBACAC;gBACAR;gBACAA;gBACA;gBACAS;kBACApB;kBACAC;kBACAoB;kBACAF;kBACAG,uCACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBAAA;gBACAb;gBACA;kBAAA;kBACAG;oBACAC;oBACAC;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAS;MACA;MACA;QAAA;QAAA;UAAAC;QAAA;QACA;QACAC;UACAC;UACAD;QACA;MACA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBACA;gBAEAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAhB;kBACAE;kBACAD;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAGAe;kBAAA;kBAAA;gBAAA;gBACAhB;kBACAE;kBACAD;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAGAe;kBAAA;kBAAA;gBAAA;gBACAhB;kBACAE;kBACAD;gBACA;gBAAA;cAAA;gBAAA,MAGAe;kBAAA;kBAAA;gBAAA;gBACAhB;kBACAE;kBACAD;gBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAD;kBACAE;kBACAe;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAV;gCAAA;gCAAA;8BAAA;8BAAA,IAEAV;gCAAA;gCAAA;8BAAA;8BACAA;gCACAiB;gCACAI;8BACA;8BACA;8BAAA;4BAAA;8BAAA;8BAAA;8BAAA,OAMA;gCACAL;gCACAM;8BACA;4BAAA;8BAHAZ;8BAIAb;8BAAA,MACAa;gCAAA;gCAAA;8BAAA;8BACAV;gCACAC;gCACAC;8BACA;8BACA;8BAAA;4BAAA;8BAAA,IAGAQ;gCAAA;gCAAA;8BAAA;8BACAV;gCACAE;gCACAD;8BACA;8BACA;8BAAA;4BAAA;8BAAA,MAGAS;gCAAA;gCAAA;8BAAA;8BACAV;gCACAC;gCACAC;8BACA;8BACA;8BAAA;4BAAA;8BAGA;8BACAF;gCACAR;gCACA+B;gCACAC;gCACAJ;kCACA;oCACApB;sCACAC;sCACAC;oCACA;oCACAuB;sCACAzB;wCACA0B;sCACA;oCACA;kCACA;oCACA1B;sCACAE;sCACAD;oCACA;kCACA;gCACA;gCACA0B;kCACA3B;oCACAE;oCACAD;kCACA;gCACA;gCACA2B;kCACA;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEA5B;gCACAE;gCACAD;8BACA;8BACA;4BAAA;8BAAA;8BAAA;4BAAA;8BAGA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA4B;MACA;IACA;IACAC;MACA;IAAA,CACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAArB;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;kBACAE;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+B;MACAhC;QACAlB;QACAsC;UACApB;YACAE;YACAD;UACA;QACA;QACA0B;UACA3B;YACAE;YACAD;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjVA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/coachCashOut.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/coachCashOut.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coachCashOut.vue?vue&type=template&id=56491a8a&scoped=true&\"\nvar renderjs\nimport script from \"./coachCashOut.vue?vue&type=script&lang=js&\"\nexport * from \"./coachCashOut.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coachCashOut.vue?vue&type=style&index=0&id=56491a8a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"56491a8a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/coachCashOut.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=template&id=56491a8a&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showNameIdModal = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"left\">提现至</view>\r\n\t\t\t<view class=\"right\">微信</view>\r\n\t\t</view>\r\n\t\t<view class=\"mid\">\r\n\t\t\t<view class=\"title\">提现金额</view>\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<view class=\"t_left\">\r\n\t\t\t\t\t<u--input placeholder=\"请输入提现金额\" type=\"number\" border=\"none\" v-model=\"money\" @change=\"change\"\r\n\t\t\t\t\t\tprefixIcon=\"rmb\"></u--input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"r_left\" @tap=\"goAll\">全部提现</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"bottom\">可提现金额￥{{ allmoney }}</view>\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @tap=\"debounceConfirmTx\" :disabled=\"isSubmitting\">确认提现</view>\r\n\t\t<text class=\"tips\">温馨提示：提现申请发起后，预计3个工作日内到账。</text>\r\n\t\t<text class=\"contact\">有问题请联系客服 <text class=\"phone\" @tap=\"copyPhoneNumber\">4008326986</text></text>\r\n\t\t<u-modal :show=\"showNameIdModal\" title=\"请完善实名信息以确保提现安全\" confirmText=\"保存\" showCancelButton @confirm=\"saveNameIdInfo\"\r\n\t\t\t@cancel=\"showNameIdModal = false\"\r\n\t\t\t:contentStyle=\"{ padding: '40rpx', background: '#ffffff', borderRadius: '16rpx' }\">\r\n\t\t\t<view class=\"slot-content\">\r\n\t\t\t\t<view class=\"main_item\">\r\n\t\t\t\t\t<view class=\"title\"><span>*</span>姓名</view>\r\n\t\t\t\t\t<input type=\"text\" v-model=\"tempForm.coachName\" placeholder=\"请输入姓名\" class=\"modal-input\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"main_item\">\r\n\t\t\t\t\t<view class=\"title\"><span>*</span>身份证号</view>\r\n\t\t\t\t\t<input type=\"text\" v-model=\"tempForm.idCode\" placeholder=\"请输入身份证号\" class=\"modal-input\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"main_item\">\r\n\t\t\t\t\t<view class=\"title\"><span>*</span>上传身份证照片</view>\r\n\t\t\t\t\t<view class=\"card\">\r\n\t\t\t\t\t\t<view class=\"card_item\">\r\n\t\t\t\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t\t\t\t<view class=\"das\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"up\">\r\n\t\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUploadTemp\" @del=\"imgUploadTemp\"\r\n\t\t\t\t\t\t\t\t\t\t\t:imagelist=\"tempForm.id_card1\" imgtype=\"id_card1\" imgclass=\"id_card_box\"\r\n\t\t\t\t\t\t\t\t\t\t\ttext=\"身份证人像面\" :imgsize=\"1\"></upload>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom\">拍摄人像面</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_item\">\r\n\t\t\t\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t\t\t\t<view class=\"das\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"up\">\r\n\t\t\t\t\t\t\t\t\t\t<upload @upload=\"imgUploadTemp\" @del=\"imgUploadTemp\"\r\n\t\t\t\t\t\t\t\t\t\t\t:imagelist=\"tempForm.id_card2\" imgtype=\"id_card2\" imgclass=\"id_card_box\"\r\n\t\t\t\t\t\t\t\t\t\t\ttext=\"身份证国徽面\" :imgsize=\"1\"></upload>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom\">拍摄国徽面</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-modal>\r\n\t</view>\r\n\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmoney: '',\r\n\t\t\t\tallmoney: '0',\r\n\t\t\t\ttempForm: { // Temporary form for name/ID input\r\n\t\t\t\t\tcoachName: '',\r\n\t\t\t\t\tidCode: '',\r\n\t\t\t\t\tid_card1: [],\r\n\t\t\t\t\tid_card2: [],\r\n\t\t\t\t},\r\n\t\t\t\tisSubmitting: false,\r\n\t\t\t\tshowNameIdModal: false, // New state for the name/ID modal\r\n\t\t\t\tmchId: '1648027588', // Replace with your actual Merchant ID\r\n\t\t\t\tdebounceConfirmTx: null, // 用于存储防抖函数\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getMoney();\r\n\t\t\t// 初始化防抖函数\r\n\t\t\tthis.debounceConfirmTx = this.debounce(this.confirmTx.bind(this), 1000); // 1秒内只允许触发一次\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\timgUploadTemp(e) {\r\n\t\t\t\tconsole.log('imgUploadTemp event:', e);\r\n\t\t\t\tconst { imagelist, imgtype } = e;\r\n\t\t\t\tthis.$set(this.tempForm, imgtype, imagelist);\r\n\t\t\t},\r\n\t\t\tasync saveNameIdInfo() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcoachName,\r\n\t\t\t\t\tidCode,\r\n\t\t\t\t\tid_card1,\r\n\t\t\t\t\tid_card2\r\n\t\t\t\t} = this.tempForm;\r\n\r\n\t\t\t\tif (!coachName || !idCode || id_card1.length === 0 || id_card2.length === 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请填写所有必填项并上传照片'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet p = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n\t\t\t\tif (!p.test(idCode)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '请填写正确的身份证号',\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet shifuid = JSON.parse(uni.getStorageSync('shiInfo'))\r\n\t\t\t\tlet userId = (uni.getStorageSync('userId'))\r\n\t\t\t\tconsole.log(shifuid)\r\n\t\t\t\tconsole.log(userId)\r\n\t\t\t\t// Construct the payload for saving name and ID card information\r\n\t\t\t\tconst payload = {\r\n\t\t\t\t\tcoachName: coachName,\r\n\t\t\t\t\tidCode: idCode,\r\n\t\t\t\t\tid: shifuid.id,\r\n\t\t\t\t\tuserId: userId,\r\n\t\t\t\t\tidCard: [id_card1[0].path, id_card2[0]\r\n\t\t\t\t\t.path], // Assuming imgsize is 1, so only one image per type\r\n\t\t\t\t};\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await this.$api.shifu.updataInfoSF(payload); // Replace with your actual API call\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code === \"200\") { // Assuming \"0\" means success\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '身份信息保存成功',\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.showNameIdModal = false;\r\n\t\t\t\t\t\t// You might want to re-attempt the quote submission or refresh data here\r\n\t\t\t\t\t\t// For now, let's just close the modal.\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: res.msg || '身份信息保存失败'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'error',\r\n\t\t\t\t\t\ttitle: error.message || '身份信息保存失败'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 防抖函数\r\n\t\t\tdebounce(fn, delay) {\r\n\t\t\t\tlet timer = null;\r\n\t\t\t\treturn function(...args) {\r\n\t\t\t\t\tif (timer) clearTimeout(timer);\r\n\t\t\t\t\ttimer = setTimeout(() => {\r\n\t\t\t\t\t\tfn.apply(this, args);\r\n\t\t\t\t\t\ttimer = null;\r\n\t\t\t\t\t}, delay);\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\tasync confirmTx() {\r\n\t\t\t\tif (this.isSubmitting) return; // 防止重复点击\r\n\t\t\t\tthis.isSubmitting = true; // 立即禁用按钮\r\n\r\n\t\t\t\tconst amount = Number(this.money);\r\n\t\t\t\tif (!amount || amount <= 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入有效的提现金额',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (amount > Number(this.allmoney)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '超过可提现金额',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (amount > 800) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '最高提现金额为799元',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (amount < 1) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '最低提现金额为1元',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Show confirmation modal before proceeding\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '确认提现',\r\n\t\t\t\t\tcontent: '为确保您的账户余额准确无误，提现操作一旦提交，请不要中途退出或刷新页面，若您在提现过程中中止操作，可能会导致余额错误，需等待1-3个工作日处理您的请求。',\r\n\t\t\t\t\tconfirmText: '确定',\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// Proceed with withdrawal only if user confirms\r\n\t\t\t\t\t\t\tif (!uni.canIUse('requestMerchantTransfer')) {\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\tcontent: '你的微信版本过低，请更新至最新版本。',\r\n\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t// Request signed package from backend\r\n\t\t\t\t\t\t\t\tconst res = await this.$api.mine.applyWallet({\r\n\t\t\t\t\t\t\t\t\tamount: this.money,\r\n\t\t\t\t\t\t\t\t\ttype: 2\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\t\tif (res.data === -5) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthis.showNameIdModal = true; // Show the new modal\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (!res.data.packageInfo) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg || '无法生成提现请求',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (res.code === \"-1\") {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// Initiate WeChat transfer\r\n\t\t\t\t\t\t\t\tuni.requestMerchantTransfers({\r\n\t\t\t\t\t\t\t\t\tmchId: res.data.mchId,\r\n\t\t\t\t\t\t\t\t\tappId: res.data.appId,\r\n\t\t\t\t\t\t\t\t\tpackage: res.data.packageInfo,\r\n\t\t\t\t\t\t\t\t\tsuccess: (transferRes) => {\r\n\t\t\t\t\t\t\t\t\t\tif (transferRes.result === 'success') {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '提现申请提交成功',\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\turl: '/shifu/income'\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '提现申请失败，请稍后重试',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: (transferRes) => {\r\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: transferRes.errMsg || '提现失败，请稍后重试',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '请稍后重试',\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.isSubmitting = false; // 用户取消时恢复按钮状态\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoAll() {\r\n\t\t\t\tthis.money = this.allmoney;\r\n\t\t\t},\r\n\t\t\tchange(e) {\r\n\t\t\t\t// Handle input change if needed\r\n\t\t\t},\r\n\t\t\tasync getMoney() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await this.$api.shifu.coachCash();\r\n\t\t\t\t\tthis.allmoney = res.servicePrice || '0';\r\n\t\t\t\t\tthis.money = this.allmoney;\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取可提现金额失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcopyPhoneNumber() {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: '4008326986',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '客服电话已复制',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制失败，请稍后重试',\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.page {\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding: 20rpx 0;\r\n\r\n\t\t.header {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #3b3b3b;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\twidth: 750rpx;\r\n\t\t\theight: 118rpx;\r\n\t\t\tbackground: #ffffff;\r\n\r\n\t\t\t.right {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.mid {\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\twidth: 750rpx;\r\n\t\t\theight: 276rpx;\r\n\t\t\tbackground: #ffffff;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tpadding-top: 40rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #3b3b3b;\r\n\t\t\t}\r\n\r\n\t\t\t.top {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: flex-end;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding-top: 28rpx;\r\n\t\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\tborder-bottom: 2rpx solid #f2f3f6;\r\n\r\n\t\t\t\t.r_left {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #e51837;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.bottom {\r\n\t\t\t\tpadding-top: 20rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.btn {\r\n\t\t\tmargin: 60rpx auto 0;\r\n\t\t\twidth: 690rpx;\r\n\t\t\theight: 98rpx;\r\n\t\t\tbackground: #2e80fe;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #ffffff;\r\n\t\t\tline-height: 98rpx;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t&:disabled {\r\n\t\t\t\tbackground: #cccccc;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.tips {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #999999;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t.contact {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #999999;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t.phone {\r\n\t\t\t\tcolor: #2e80fe;\r\n\t\t\t\ttext-decoration: underline;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.slot-content {\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\t\r\n\t.main_item {\r\n\t\tmargin-bottom: 32rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\r\n\t\t.title {\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #1a1a1a;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\r\n\t\t\tspan {\r\n\t\t\t\tcolor: #e72427;\r\n\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.modal-input {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 80rpx;\r\n\t\t\tbackground: #f8f8f8;\r\n\t\t\tborder: 1rpx solid #e5e7eb;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\tpadding: 0 24rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\ttransition: all 0.2s ease-in-out;\r\n\t\r\n\t\t\t&:focus {\r\n\t\t\t\tborder-color: #2e80fe;\r\n\t\t\t\tbackground: #ffffff;\r\n\t\t\t\tbox-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);\r\n\t\t\t}\r\n\t\r\n\t\t\t&:disabled {\r\n\t\t\t\tbackground: #f0f0f0;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tcursor: not-allowed;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.card {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tgap: 16rpx;\r\n\t\r\n\t\t\t.card_item {\r\n\t\t\t\twidth: 48%;\r\n\t\t\t\tbackground: #f2fafe;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\ttransition: transform 0.2s ease-in-out;\r\n\t\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\ttransform: translateY(-4rpx);\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.top {\r\n\t\t\t\t\theight: 180rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tpadding-top: 20rpx;\r\n\t\r\n\t\t\t\t\t.das {\r\n\t\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\t\twidth: 85%;\r\n\t\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\t\tborder: 2rpx dashed #2e80fe;\r\n\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\r\n\t\t\t\t\t\t.up {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\r\n\t\t\t\t.bottom {\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 60rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}}\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=style&index=0&id=56491a8a&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coachCashOut.vue?vue&type=style&index=0&id=56491a8a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754707288701\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}