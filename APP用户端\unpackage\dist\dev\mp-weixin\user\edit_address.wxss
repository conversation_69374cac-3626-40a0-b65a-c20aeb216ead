@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-a5731d36 {
  height: 100vh;
  background-color: #fff;
}
.page .top.data-v-a5731d36 {
  width: 750rpx;
  height: 58rpx;
  background: #fff7f1;
  font-size: 28rpx;
  font-weight: 400;
  color: #fe921b;
  line-height: 58rpx;
  text-align: center;
}
.page .btn.data-v-a5731d36 {
  margin: 0 auto;
  margin-top: 88rpx;
  width: 690rpx;
  height: 98rpx;
  background: #2e80fe;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 98rpx;
  text-align: center;
}
.page .btnD.data-v-a5731d36 {
  margin: 0 auto;
  margin-top: 40rpx;
  width: 690rpx;
  height: 98rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #2e80fe;
  line-height: 98rpx;
  text-align: center;
  border: 2rpx solid #2e80fe;
}
.page .main.data-v-a5731d36 {
  padding: 0 30rpx;
}
.page .main .main_item.data-v-a5731d36 {
  padding: 40rpx 0;
  border-bottom: 2rpx solid #e9e9e9;
  display: flex;
  align-items: center;
  position: relative;
}
.page .main .main_item .name.data-v-a5731d36 {
  width: 112rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-right: 40rpx;
}
.page .main .main_item .address.data-v-a5731d36 {
  font-size: 28rpx;
  font-weight: 400;
  color: #adadad;
}
.page .main .main_item image.data-v-a5731d36 {
  width: 23rpx;
  height: 27rpx;
  position: absolute;
  right: 0;
  top: 46rpx;
}
.page .main .main_item input.data-v-a5731d36 {
  width: 450rpx;
  font-size: 28rpx;
  color: #333333;
}
.page .main .main_item .box.data-v-a5731d36 {
  display: flex;
  align-items: center;
}
.page .main .main_item .box .box_item.data-v-a5731d36 {
  margin-right: 20rpx;
  width: 88rpx;
  height: 50rpx;
  background: #ffffff;
  border-radius: 4rpx;
  border: 2rpx solid #ededed;
  font-size: 28rpx;
  font-weight: 400;
  color: #adadad;
  line-height: 46rpx;
  text-align: center;
}
.page .main .last.data-v-a5731d36 {
  justify-content: space-between;
}
.page .main .last .name.data-v-a5731d36 {
  width: 170rpx;
}

